/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"strings"
	"testing"

	diagnosticscommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/diagnostics/common"
	workspacecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/workspace/common"
)

func TestDiagnosticsService(t *testing.T) {
	service := NewDiagnosticsService()

	t.Run("ServiceBrand", func(t *testing.T) {
		brand := service.ServiceBrand()
		// Should not panic and can be nil
		_ = brand
	})

	t.Run("GetProcessTree", func(t *testing.T) {
		processTree, err := service.GetProcessTree()
		if err != nil {
			t.Errorf("GetProcessTree should not return error: %v", err)
		}

		if processTree == nil {
			t.Error("GetProcessTree should not return nil")
		}

		if processTree.PID <= 0 {
			t.Error("Process PID should be positive")
		}

		if processTree.Name == "" {
			t.Error("Process name should not be empty")
		}
	})

	t.Run("GetSystemInfo", func(t *testing.T) {
		mainProcessInfo := &diagnosticscommon.IMainProcessDiagnostics{
			MainPID:       12345,
			MainArguments: []string{"test", "--flag"},
			Windows:       []*diagnosticscommon.IWindowDiagnostics{},
			PidToNames:    []*diagnosticscommon.IProcessDiagnostics{},
			ScreenReader:  false,
		}

		remoteInfo := []*diagnosticscommon.IRemoteDiagnosticEntry{}

		systemInfo, err := service.GetSystemInfo(mainProcessInfo, remoteInfo)
		if err != nil {
			t.Errorf("GetSystemInfo should not return error: %v", err)
		}

		if systemInfo == nil {
			t.Error("GetSystemInfo should not return nil")
		}

		if systemInfo.OS == "" {
			t.Error("OS should not be empty")
		}

		if systemInfo.Memory == "" {
			t.Error("Memory should not be empty")
		}

		if systemInfo.VMHint == "" {
			t.Error("VMHint should not be empty")
		}
	})

	t.Run("GetPerformanceInfo", func(t *testing.T) {
		mainProcessInfo := &diagnosticscommon.IMainProcessDiagnostics{
			MainPID:       12345,
			MainArguments: []string{"test", "--flag"},
			Windows:       []*diagnosticscommon.IWindowDiagnostics{},
			PidToNames:    []*diagnosticscommon.IProcessDiagnostics{},
			ScreenReader:  false,
		}

		remoteInfo := []*diagnosticscommon.IRemoteDiagnosticEntry{}

		perfInfo, err := service.GetPerformanceInfo(mainProcessInfo, remoteInfo)
		if err != nil {
			t.Errorf("GetPerformanceInfo should not return error: %v", err)
		}

		if perfInfo == nil {
			t.Error("GetPerformanceInfo should not return nil")
		}

		if perfInfo.ProcessInfo == nil {
			t.Error("ProcessInfo should not be nil")
		}
	})

	t.Run("GetDiagnostics", func(t *testing.T) {
		mainProcessInfo := &diagnosticscommon.IMainProcessDiagnostics{
			MainPID:       12345,
			MainArguments: []string{"test", "--flag"},
			Windows:       []*diagnosticscommon.IWindowDiagnostics{},
			PidToNames:    []*diagnosticscommon.IProcessDiagnostics{},
			ScreenReader:  false,
		}

		remoteInfo := []*diagnosticscommon.IRemoteDiagnosticEntry{}

		diagnostics, err := service.GetDiagnostics(mainProcessInfo, remoteInfo)
		if err != nil {
			t.Errorf("GetDiagnostics should not return error: %v", err)
		}

		if diagnostics == "" {
			t.Error("Diagnostics should not be empty")
		}

		// Should contain system information
		if !contains(diagnostics, "System Information") {
			t.Error("Diagnostics should contain system information")
		}
	})

	t.Run("GetWorkspaceFileExtensions", func(t *testing.T) {
		// Test with nil workspace
		extensions, err := service.GetWorkspaceFileExtensions(nil)
		if err != nil {
			t.Errorf("GetWorkspaceFileExtensions should not return error: %v", err)
		}

		if extensions == nil {
			t.Error("GetWorkspaceFileExtensions should not return nil")
		}

		if extensions.Extensions == nil {
			t.Error("Extensions array should not be nil")
		}

		// Test with empty workspace
		workspace := &workspacecommon.IWorkspace{
			Folders: []*workspacecommon.IWorkspaceFolder{},
		}

		extensions, err = service.GetWorkspaceFileExtensions(workspace)
		if err != nil {
			t.Errorf("GetWorkspaceFileExtensions should not return error: %v", err)
		}

		if extensions == nil {
			t.Error("GetWorkspaceFileExtensions should not return nil")
		}
	})

	t.Run("CollectWorkspaceStats", func(t *testing.T) {
		// Test with empty folder paths
		stats, err := service.CollectWorkspaceStats([]string{})
		if err != nil {
			t.Errorf("CollectWorkspaceStats should not return error: %v", err)
		}

		if stats == nil {
			t.Error("CollectWorkspaceStats should not return nil")
		}

		if stats.FileTypes == nil {
			t.Error("FileTypes should not be nil")
		}

		if stats.ConfigFiles == nil {
			t.Error("ConfigFiles should not be nil")
		}

		if stats.LaunchConfigFiles == nil {
			t.Error("LaunchConfigFiles should not be nil")
		}
	})

	t.Run("ReportWorkspaceStats", func(t *testing.T) {
		workspaceInfo := &diagnosticscommon.IWorkspaceInformation{
			IWorkspace: &workspacecommon.IWorkspace{
				Folders: []*workspacecommon.IWorkspaceFolder{},
			},
			TelemetryID:       nil,
			RendererSessionID: "test-session",
		}

		err := service.ReportWorkspaceStats(workspaceInfo)
		if err != nil {
			t.Errorf("ReportWorkspaceStats should not return error: %v", err)
		}
	})
}

func TestNullDiagnosticsService(t *testing.T) {
	service := &diagnosticscommon.NullDiagnosticsService{}

	t.Run("ServiceBrand", func(t *testing.T) {
		brand := service.ServiceBrand()
		// Should not panic and can be nil
		_ = brand
	})

	t.Run("GetPerformanceInfo", func(t *testing.T) {
		perfInfo, err := service.GetPerformanceInfo(nil, nil)
		if err != nil {
			t.Errorf("GetPerformanceInfo should not return error: %v", err)
		}

		if perfInfo == nil {
			t.Error("GetPerformanceInfo should not return nil")
		}
	})

	t.Run("GetSystemInfo", func(t *testing.T) {
		systemInfo, err := service.GetSystemInfo(nil, nil)
		if err != nil {
			t.Errorf("GetSystemInfo should not return error: %v", err)
		}

		if systemInfo == nil {
			t.Error("GetSystemInfo should not return nil")
		}

		if systemInfo.OS != "nullOs" {
			t.Error("OS should be 'nullOs' in null service")
		}
	})

	t.Run("GetDiagnostics", func(t *testing.T) {
		diagnostics, err := service.GetDiagnostics(nil, nil)
		if err != nil {
			t.Errorf("GetDiagnostics should not return error: %v", err)
		}

		if diagnostics != "" {
			t.Error("Diagnostics should be empty in null service")
		}
	})

	t.Run("GetWorkspaceFileExtensions", func(t *testing.T) {
		extensions, err := service.GetWorkspaceFileExtensions(nil)
		if err != nil {
			t.Errorf("GetWorkspaceFileExtensions should not return error: %v", err)
		}

		if extensions == nil {
			t.Error("GetWorkspaceFileExtensions should not return nil")
		}

		if len(extensions.Extensions) != 0 {
			t.Error("Extensions should be empty in null service")
		}
	})

	t.Run("ReportWorkspaceStats", func(t *testing.T) {
		err := service.ReportWorkspaceStats(nil)
		if err != nil {
			t.Errorf("ReportWorkspaceStats should not return error: %v", err)
		}
	})
}

func TestIsRemoteDiagnosticError(t *testing.T) {
	t.Run("WithError", func(t *testing.T) {
		entry := &diagnosticscommon.IRemoteDiagnosticEntry{
			Error: &diagnosticscommon.IRemoteDiagnosticError{
				HostName:     "test-host",
				ErrorMessage: "test error",
			},
		}

		if !diagnosticscommon.IsRemoteDiagnosticError(entry) {
			t.Error("Should detect error entry")
		}
	})

	t.Run("WithInfo", func(t *testing.T) {
		entry := &diagnosticscommon.IRemoteDiagnosticEntry{
			Info: &diagnosticscommon.IRemoteDiagnosticInfo{
				HostName: "test-host",
			},
		}

		if diagnosticscommon.IsRemoteDiagnosticError(entry) {
			t.Error("Should not detect info entry as error")
		}
	})

	t.Run("WithDirectError", func(t *testing.T) {
		err := &diagnosticscommon.IRemoteDiagnosticError{
			HostName:     "test-host",
			ErrorMessage: "test error",
		}

		if !diagnosticscommon.IsRemoteDiagnosticError(err) {
			t.Error("Should detect direct error")
		}
	})

	t.Run("WithInvalidInput", func(t *testing.T) {
		if diagnosticscommon.IsRemoteDiagnosticError("invalid") {
			t.Error("Should not detect invalid input as error")
		}
	})
}

// Helper function
func contains(s, substr string) bool {
	return len(s) > 0 && len(substr) > 0 &&
		len(s) >= len(substr) &&
		strings.Contains(s, substr)
}
