/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"fmt"
	"io/fs"
	"os"
	"path/filepath"
	"runtime"
	"strings"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	diagnosticscommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/diagnostics/common"
	workspacecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/workspace/common"
)

// DiagnosticsService implements IDiagnosticsService for Node.js environments
type DiagnosticsService struct {
	// Service brand
	serviceBrand interface{}
}

// NewDiagnosticsService creates a new DiagnosticsService
func NewDiagnosticsService() *DiagnosticsService {
	return &DiagnosticsService{}
}

// ServiceBrand returns the service brand
func (d *DiagnosticsService) ServiceBrand() interface{} {
	return d.serviceBrand
}

// GetPerformanceInfo gathers performance information
func (d *DiagnosticsService) GetPerformanceInfo(mainProcessInfo *diagnosticscommon.IMainProcessDiagnostics, remoteInfo []*diagnosticscommon.IRemoteDiagnosticEntry) (*diagnosticscommon.PerformanceInfo, error) {
	sw := basecommon.CreateAndStart()
	defer sw.Stop()

	processTree, err := d.GetProcessTree()
	if err != nil {
		return nil, err
	}

	processInfo := d.formatProcessList(processTree, 0)
	workspaceInfo := ""

	return &diagnosticscommon.PerformanceInfo{
		ProcessInfo:   &processInfo,
		WorkspaceInfo: &workspaceInfo,
	}, nil
}

// GetSystemInfo gathers system information
func (d *DiagnosticsService) GetSystemInfo(mainProcessInfo *diagnosticscommon.IMainProcessDiagnostics, remoteInfo []*diagnosticscommon.IRemoteDiagnosticEntry) (*diagnosticscommon.SystemInfo, error) {
	sw := basecommon.CreateAndStart()
	defer sw.Stop()

	machineInfo, err := d.getMachineInfo()
	if err != nil {
		return nil, err
	}

	processArgs := strings.Join(os.Args, " ")
	gpuStatus := d.getGPUStatus()
	screenReader := d.getScreenReaderStatus()
	loadInfo := d.getLoadInfo()

	return &diagnosticscommon.SystemInfo{
		IMachineInfo: machineInfo,
		ProcessArgs:  processArgs,
		GPUStatus:    gpuStatus,
		ScreenReader: screenReader,
		RemoteData:   remoteInfo,
		Load:         loadInfo,
	}, nil
}

// GetDiagnostics returns formatted diagnostic information
func (d *DiagnosticsService) GetDiagnostics(mainProcessInfo *diagnosticscommon.IMainProcessDiagnostics, remoteInfo []*diagnosticscommon.IRemoteDiagnosticEntry) (string, error) {
	systemInfo, err := d.GetSystemInfo(mainProcessInfo, remoteInfo)
	if err != nil {
		return "", err
	}

	return d.formatDiagnostics(systemInfo, mainProcessInfo, remoteInfo), nil
}

// GetWorkspaceFileExtensions scans workspace for file extensions
func (d *DiagnosticsService) GetWorkspaceFileExtensions(workspace *workspacecommon.IWorkspace) (*diagnosticscommon.WorkspaceFileExtensions, error) {
	if workspace == nil {
		return &diagnosticscommon.WorkspaceFileExtensions{
			Extensions: []string{},
		}, nil
	}

	extensionMap := make(map[string]bool)

	// Process workspace folders
	if workspace.Folders != nil {
		for _, folder := range workspace.Folders {
			if folder.URI != nil {
				err := d.scanFolderForExtensions(folder.URI.GetFSPath(), extensionMap)
				if err != nil {
					// Log error but continue with other folders
					continue
				}
			}
		}
	}

	// Convert map to slice
	extensions := make([]string, 0, len(extensionMap))
	for ext := range extensionMap {
		extensions = append(extensions, ext)
	}

	return &diagnosticscommon.WorkspaceFileExtensions{
		Extensions: extensions,
	}, nil
}

// ReportWorkspaceStats reports workspace statistics
func (d *DiagnosticsService) ReportWorkspaceStats(workspace *diagnosticscommon.IWorkspaceInformation) error {
	// In the TypeScript version, this method sends telemetry
	// For now, we'll just return nil as a placeholder
	return nil
}

// Private helper methods

// getMachineInfo gathers machine information
func (d *DiagnosticsService) getMachineInfo() (*diagnosticscommon.IMachineInfo, error) {
	info := &diagnosticscommon.IMachineInfo{
		OS:     runtime.GOOS,
		Memory: d.getMemoryInfo(),
		VMHint: d.getVMHint(),
	}

	// Get CPU information
	cpuInfo := d.getCPUInfo()
	if cpuInfo != "" {
		info.CPUs = &cpuInfo
	}

	// Get Linux environment if on Linux
	if runtime.GOOS == "linux" {
		info.LinuxEnv = d.getLinuxEnvironment()
	}

	return info, nil
}

// getCPUInfo returns CPU information
func (d *DiagnosticsService) getCPUInfo() string {
	// On Go, we can use runtime.NumCPU() to get the number of CPUs
	return fmt.Sprintf("%d logical processors", runtime.NumCPU())
}

// getMemoryInfo returns memory information
func (d *DiagnosticsService) getMemoryInfo() string {
	// Get memory stats from runtime
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// Convert bytes to MB for readability
	allocMB := m.Alloc / 1024 / 1024
	totalAllocMB := m.TotalAlloc / 1024 / 1024
	sysMB := m.Sys / 1024 / 1024

	return fmt.Sprintf("Alloc: %d MB, TotalAlloc: %d MB, Sys: %d MB", allocMB, totalAllocMB, sysMB)
}

// getVMHint returns VM hint information
func (d *DiagnosticsService) getVMHint() string {
	// Simple heuristic to detect if running in a VM
	if runtime.GOOS == "linux" {
		// Check for common VM indicators
		if d.checkFileExists("/proc/xen") ||
			d.checkFileExists("/sys/bus/acpi/devices/VMGN0001:00") ||
			d.checkFileExists("/sys/class/dmi/id/product_name") {
			return "VM (detected)"
		}
	}
	return "Native"
}

// getLinuxEnvironment returns Linux environment information
func (d *DiagnosticsService) getLinuxEnvironment() *diagnosticscommon.ILinuxEnv {
	env := &diagnosticscommon.ILinuxEnv{}

	if val := os.Getenv("DESKTOP_SESSION"); val != "" {
		env.DesktopSession = &val
	}
	if val := os.Getenv("XDG_SESSION_DESKTOP"); val != "" {
		env.XdgSessionDesktop = &val
	}
	if val := os.Getenv("XDG_CURRENT_DESKTOP"); val != "" {
		env.XdgCurrentDesktop = &val
	}
	if val := os.Getenv("XDG_SESSION_TYPE"); val != "" {
		env.XdgSessionType = &val
	}

	return env
}

// getGPUStatus returns GPU status information
func (d *DiagnosticsService) getGPUStatus() interface{} {
	// Placeholder implementation
	return map[string]interface{}{
		"available": false,
		"info":      "Not implemented in Go version",
	}
}

// getScreenReaderStatus returns screen reader status
func (d *DiagnosticsService) getScreenReaderStatus() string {
	// Placeholder implementation - would need platform-specific code
	return "unknown"
}

// getLoadInfo returns system load information
func (d *DiagnosticsService) getLoadInfo() *string {
	// On Unix systems, we could read /proc/loadavg
	if runtime.GOOS == "linux" {
		if data, err := os.ReadFile("/proc/loadavg"); err == nil {
			loadStr := strings.TrimSpace(string(data))
			return &loadStr
		}
	}
	return nil
}

// scanFolderForExtensions scans a folder for file extensions
func (d *DiagnosticsService) scanFolderForExtensions(folderPath string, extensionMap map[string]bool) error {
	const maxFiles = 10000 // Limit to prevent excessive scanning

	fileCount := 0
	return filepath.WalkDir(folderPath, func(path string, info fs.DirEntry, err error) error {
		if err != nil {
			return nil // Skip errors and continue
		}

		if fileCount >= maxFiles {
			return filepath.SkipAll
		}

		if !info.IsDir() {
			ext := filepath.Ext(path)
			if ext != "" {
				extensionMap[ext] = true
			}
			fileCount++
		}

		return nil
	})
}

// formatProcessList formats the process list for display
func (d *DiagnosticsService) formatProcessList(process *diagnosticscommon.ProcessItem, depth int) string {
	if process == nil {
		return ""
	}

	var sb strings.Builder
	indent := strings.Repeat("  ", depth)

	// Format current process
	sb.WriteString(fmt.Sprintf("%s%s (%d)", indent, process.Name, process.PID))
	if process.Load != nil {
		sb.WriteString(fmt.Sprintf(" - Load: %.2f%%", *process.Load))
	}
	sb.WriteString("\n")

	// Format children
	if process.Children != nil {
		for _, child := range process.Children {
			sb.WriteString(d.formatProcessList(child, depth+1))
		}
	}

	return sb.String()
}

// formatDiagnostics formats diagnostic information into a readable string
func (d *DiagnosticsService) formatDiagnostics(systemInfo *diagnosticscommon.SystemInfo, mainProcessInfo *diagnosticscommon.IMainProcessDiagnostics, remoteInfo []*diagnosticscommon.IRemoteDiagnosticEntry) string {
	var sb strings.Builder

	// System Information
	sb.WriteString("## System Information\n\n")
	sb.WriteString(fmt.Sprintf("- OS: %s\n", systemInfo.OS))
	sb.WriteString(fmt.Sprintf("- Memory: %s\n", systemInfo.Memory))
	sb.WriteString(fmt.Sprintf("- VM Hint: %s\n", systemInfo.VMHint))
	if systemInfo.CPUs != nil {
		sb.WriteString(fmt.Sprintf("- CPUs: %s\n", *systemInfo.CPUs))
	}
	if systemInfo.Load != nil {
		sb.WriteString(fmt.Sprintf("- Load: %s\n", *systemInfo.Load))
	}
	sb.WriteString(fmt.Sprintf("- Screen Reader: %s\n", systemInfo.ScreenReader))

	// Process Information
	if mainProcessInfo != nil {
		sb.WriteString("\n## Process Information\n\n")
		sb.WriteString(fmt.Sprintf("- Main PID: %d\n", mainProcessInfo.MainPID))
		sb.WriteString(fmt.Sprintf("- Arguments: %s\n", strings.Join(mainProcessInfo.MainArguments, " ")))
		sb.WriteString(fmt.Sprintf("- Screen Reader: %t\n", mainProcessInfo.ScreenReader))

		if len(mainProcessInfo.Windows) > 0 {
			sb.WriteString(fmt.Sprintf("- Windows: %d\n", len(mainProcessInfo.Windows)))
			for i, window := range mainProcessInfo.Windows {
				sb.WriteString(fmt.Sprintf("  - Window %d: %s (PID: %d)\n", i+1, window.Title, window.PID))
			}
		}
	}

	// Remote Information
	if len(remoteInfo) > 0 {
		sb.WriteString("\n## Remote Information\n\n")
		for i, remote := range remoteInfo {
			if remote.Error != nil {
				sb.WriteString(fmt.Sprintf("- Remote %d: %s (Error: %s)\n", i+1, remote.Error.HostName, remote.Error.ErrorMessage))
			} else if remote.Info != nil {
				sb.WriteString(fmt.Sprintf("- Remote %d: %s\n", i+1, remote.Info.HostName))
				if remote.Info.Latency != nil {
					sb.WriteString(fmt.Sprintf("  - Latency: Current: %.2fms, Average: %.2fms\n", remote.Info.Latency.Current, remote.Info.Latency.Average))
				}
			}
		}
	}

	return sb.String()
}

// checkFileExists checks if a file exists
func (d *DiagnosticsService) checkFileExists(path string) bool {
	_, err := os.Stat(path)
	return err == nil
}

// GetProcessTree returns the process tree starting from current process
func (d *DiagnosticsService) GetProcessTree() (*diagnosticscommon.ProcessItem, error) {
	// This would need to use ps command or similar to get process tree
	// For now, return a simple implementation
	currentPID := os.Getpid()

	process := &diagnosticscommon.ProcessItem{
		Name:     filepath.Base(os.Args[0]),
		Cmd:      strings.Join(os.Args, " "),
		PID:      currentPID,
		PPID:     os.Getppid(),
		Children: []*diagnosticscommon.ProcessItem{},
	}

	return process, nil
}

// CollectWorkspaceStats collects workspace statistics
func (d *DiagnosticsService) CollectWorkspaceStats(folderPaths []string) (*diagnosticscommon.WorkspaceStats, error) {
	sw := basecommon.CreateAndStart()
	defer sw.Stop()

	stats := &diagnosticscommon.WorkspaceStats{
		FileTypes:         []*diagnosticscommon.WorkspaceStatItem{},
		ConfigFiles:       []*diagnosticscommon.WorkspaceStatItem{},
		LaunchConfigFiles: []*diagnosticscommon.WorkspaceStatItem{},
		TotalScanTime:     sw.ElapsedMilliseconds(),
	}

	fileTypeMap := make(map[string]int)
	configFileMap := make(map[string]int)
	launchConfigMap := make(map[string]int)

	const maxFiles = 20000
	fileCount := 0
	readdirCount := 0

	for _, folderPath := range folderPaths {
		err := filepath.WalkDir(folderPath, func(path string, info fs.DirEntry, err error) error {
			if err != nil {
				return nil
			}

			if fileCount >= maxFiles {
				stats.MaxFilesReached = true
				return filepath.SkipAll
			}

			if info.IsDir() {
				readdirCount++
				return nil
			}

			fileCount++
			fileName := info.Name()
			ext := filepath.Ext(fileName)

			// Count file types
			if ext != "" {
				fileTypeMap[ext]++
			}

			// Check for config files
			if d.isConfigFile(fileName) {
				configFileMap[fileName]++
			}

			// Check for launch config files
			if d.isLaunchConfigFile(fileName, path) {
				launchConfigMap[fileName]++
			}

			return nil
		})

		if err != nil {
			// Continue with other folders even if one fails
			continue
		}
	}

	// Convert maps to slices
	for fileType, count := range fileTypeMap {
		stats.FileTypes = append(stats.FileTypes, &diagnosticscommon.WorkspaceStatItem{
			Name:  fileType,
			Count: count,
		})
	}

	for configFile, count := range configFileMap {
		stats.ConfigFiles = append(stats.ConfigFiles, &diagnosticscommon.WorkspaceStatItem{
			Name:  configFile,
			Count: count,
		})
	}

	for launchFile, count := range launchConfigMap {
		stats.LaunchConfigFiles = append(stats.LaunchConfigFiles, &diagnosticscommon.WorkspaceStatItem{
			Name:  launchFile,
			Count: count,
		})
	}

	stats.FileCount = fileCount
	stats.TotalReaddirCount = readdirCount
	stats.TotalScanTime = sw.ElapsedMilliseconds()

	return stats, nil
}

// isConfigFile checks if a file is a configuration file
func (d *DiagnosticsService) isConfigFile(fileName string) bool {
	configFiles := []string{
		"package.json", "tsconfig.json", "jsconfig.json", ".eslintrc", ".eslintrc.json",
		"webpack.config.js", "gulpfile.js", "gruntfile.js", ".gitignore", ".gitattributes",
	}

	for _, configFile := range configFiles {
		if fileName == configFile {
			return true
		}
	}

	return false
}

// isLaunchConfigFile checks if a file is a launch configuration file
func (d *DiagnosticsService) isLaunchConfigFile(fileName, fullPath string) bool {
	if fileName == "launch.json" && strings.Contains(fullPath, ".vscode") {
		return true
	}
	return false
}
