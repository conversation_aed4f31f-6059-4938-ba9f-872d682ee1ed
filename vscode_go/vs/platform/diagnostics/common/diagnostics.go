/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	workspacecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/workspace/common"
)

// Service identifier
const ID = "diagnosticsService"

// IDiagnosticsService represents the diagnostics service interface
type IDiagnosticsService interface {
	// Service brand for type safety
	ServiceBrand() interface{}

	GetPerformanceInfo(mainProcessInfo *IMainProcessDiagnostics, remoteInfo []*IRemoteDiagnosticEntry) (*PerformanceInfo, error)
	GetSystemInfo(mainProcessInfo *IMainProcessDiagnostics, remoteInfo []*IRemoteDiagnosticEntry) (*SystemInfo, error)
	GetDiagnostics(mainProcessInfo *IMainProcessDiagnostics, remoteInfo []*IRemoteDiagnosticEntry) (string, error)
	GetWorkspaceFileExtensions(workspace *workspacecommon.IWorkspace) (*WorkspaceFileExtensions, error)
	ReportWorkspaceStats(workspace *IWorkspaceInformation) error
}

// ILinuxEnv represents Linux environment information
type ILinuxEnv struct {
	DesktopSession    *string `json:"desktopSession,omitempty"`
	XdgSessionDesktop *string `json:"xdgSessionDesktop,omitempty"`
	XdgCurrentDesktop *string `json:"xdgCurrentDesktop,omitempty"`
	XdgSessionType    *string `json:"xdgSessionType,omitempty"`
}

// IMachineInfo represents machine information
type IMachineInfo struct {
	OS       string     `json:"os"`
	CPUs     *string    `json:"cpus,omitempty"`
	Memory   string     `json:"memory"`
	VMHint   string     `json:"vmHint"`
	LinuxEnv *ILinuxEnv `json:"linuxEnv,omitempty"`
}

// ProcessItem represents a process item (from base/common/processes)
type ProcessItem struct {
	Name     string         `json:"name"`
	Cmd      string         `json:"cmd"`
	PID      int            `json:"pid"`
	PPID     int            `json:"ppid"`
	Load     *float64       `json:"load,omitempty"`
	Children []*ProcessItem `json:"children,omitempty"`
}

// IDiagnosticInfo represents diagnostic information
type IDiagnosticInfo struct {
	MachineInfo       *IMachineInfo              `json:"machineInfo"`
	WorkspaceMetadata map[string]*WorkspaceStats `json:"workspaceMetadata,omitempty"`
	Processes         *ProcessItem               `json:"processes,omitempty"`
}

// SystemInfo extends IMachineInfo with additional system information
type SystemInfo struct {
	*IMachineInfo
	ProcessArgs  string                    `json:"processArgs"`
	GPUStatus    interface{}               `json:"gpuStatus"`
	ScreenReader string                    `json:"screenReader"`
	RemoteData   []*IRemoteDiagnosticEntry `json:"remoteData"`
	Load         *string                   `json:"load,omitempty"`
}

// Latency represents latency information
type Latency struct {
	Current float64 `json:"current"`
	Average float64 `json:"average"`
}

// IRemoteDiagnosticInfo represents remote diagnostic information
type IRemoteDiagnosticInfo struct {
	*IDiagnosticInfo
	HostName string   `json:"hostName"`
	Latency  *Latency `json:"latency,omitempty"`
}

// IRemoteDiagnosticError represents a remote diagnostic error
type IRemoteDiagnosticError struct {
	HostName     string `json:"hostName"`
	ErrorMessage string `json:"errorMessage"`
}

// IRemoteDiagnosticEntry is a union type for diagnostic entries
type IRemoteDiagnosticEntry struct {
	Info  *IRemoteDiagnosticInfo  `json:"info,omitempty"`
	Error *IRemoteDiagnosticError `json:"error,omitempty"`
}

// IDiagnosticInfoOptions represents options for diagnostic information
type IDiagnosticInfoOptions struct {
	IncludeProcesses bool                        `json:"includeProcesses,omitempty"`
	Folders          []*basecommon.UriComponents `json:"folders,omitempty"`
}

// WorkspaceStatItem represents a workspace statistics item
type WorkspaceStatItem struct {
	Name  string `json:"name"`
	Count int    `json:"count"`
}

// WorkspaceStats represents workspace statistics
type WorkspaceStats struct {
	FileTypes         []*WorkspaceStatItem `json:"fileTypes"`
	ConfigFiles       []*WorkspaceStatItem `json:"configFiles"`
	FileCount         int                  `json:"fileCount"`
	MaxFilesReached   bool                 `json:"maxFilesReached"`
	LaunchConfigFiles []*WorkspaceStatItem `json:"launchConfigFiles"`
	TotalScanTime     int64                `json:"totalScanTime"`
	TotalReaddirCount int                  `json:"totalReaddirCount"`
}

// PerformanceInfo represents performance information
type PerformanceInfo struct {
	ProcessInfo   *string `json:"processInfo,omitempty"`
	WorkspaceInfo *string `json:"workspaceInfo,omitempty"`
}

// WorkspaceFileExtensions represents workspace file extensions
type WorkspaceFileExtensions struct {
	Extensions []string `json:"extensions"`
}

// IWorkspaceInformation extends IWorkspace with additional information
type IWorkspaceInformation struct {
	*workspacecommon.IWorkspace
	TelemetryID       *string `json:"telemetryId"`
	RendererSessionID string  `json:"rendererSessionId"`
}

// IWindowDiagnostics represents window diagnostic information
type IWindowDiagnostics struct {
	ID              int                         `json:"id"`
	PID             int                         `json:"pid"`
	Title           string                      `json:"title"`
	FolderURIs      []*basecommon.UriComponents `json:"folderURIs"`
	RemoteAuthority *string                     `json:"remoteAuthority,omitempty"`
}

// IProcessDiagnostics represents process diagnostic information
type IProcessDiagnostics struct {
	PID  int    `json:"pid"`
	Name string `json:"name"`
}

// IMainProcessDiagnostics represents main process diagnostic information
type IMainProcessDiagnostics struct {
	MainPID          int                    `json:"mainPID"`
	MainArguments    []string               `json:"mainArguments"`
	Windows          []*IWindowDiagnostics  `json:"windows"`
	PidToNames       []*IProcessDiagnostics `json:"pidToNames"`
	ScreenReader     bool                   `json:"screenReader"`
	GPUFeatureStatus interface{}            `json:"gpuFeatureStatus"`
}

// IsRemoteDiagnosticError checks if an entry is a remote diagnostic error
func IsRemoteDiagnosticError(x interface{}) bool {
	if entry, ok := x.(*IRemoteDiagnosticEntry); ok {
		return entry.Error != nil
	}

	if err, ok := x.(*IRemoteDiagnosticError); ok {
		return err.HostName != "" && err.ErrorMessage != ""
	}

	return false
}

// NullDiagnosticsService implements IDiagnosticsService with null behavior
type NullDiagnosticsService struct{}

// ServiceBrand returns the service brand
func (n *NullDiagnosticsService) ServiceBrand() interface{} {
	return nil
}

// GetPerformanceInfo returns empty performance info
func (n *NullDiagnosticsService) GetPerformanceInfo(mainProcessInfo *IMainProcessDiagnostics, remoteInfo []*IRemoteDiagnosticEntry) (*PerformanceInfo, error) {
	return &PerformanceInfo{}, nil
}

// GetSystemInfo returns null system info
func (n *NullDiagnosticsService) GetSystemInfo(mainProcessInfo *IMainProcessDiagnostics, remoteInfo []*IRemoteDiagnosticEntry) (*SystemInfo, error) {
	return &SystemInfo{
		IMachineInfo: &IMachineInfo{
			OS:     "nullOs",
			Memory: "nullMemory",
			VMHint: "nullVmHint",
		},
		ProcessArgs:  "nullProcessArgs",
		GPUStatus:    "nullGpuStatus",
		ScreenReader: "nullScreenReader",
		RemoteData:   make([]*IRemoteDiagnosticEntry, 0),
	}, nil
}

// GetDiagnostics returns empty diagnostics string
func (n *NullDiagnosticsService) GetDiagnostics(mainProcessInfo *IMainProcessDiagnostics, remoteInfo []*IRemoteDiagnosticEntry) (string, error) {
	return "", nil
}

// GetWorkspaceFileExtensions returns empty extensions
func (n *NullDiagnosticsService) GetWorkspaceFileExtensions(workspace *workspacecommon.IWorkspace) (*WorkspaceFileExtensions, error) {
	return &WorkspaceFileExtensions{
		Extensions: make([]string, 0),
	}, nil
}

// ReportWorkspaceStats does nothing in null implementation
func (n *NullDiagnosticsService) ReportWorkspaceStats(workspace *IWorkspaceInformation) error {
	return nil
}
