/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"encoding/json"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	environmentcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/environment/common"
	filescommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/files/common"
	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
	statenode "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/state/node"
	uriidentitycommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/uriIdentity/common"
	userDataProfileCommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/userDataProfile/common"
)

// StoredUserDataProfileState represents the stored state of a user data profile
type StoredUserDataProfileState struct {
	userDataProfileCommon.StoredUserDataProfile
	Location interface{} `json:"location"` // Can be URI or string
}

// UserDataProfilesReadonlyService provides read-only access to user data profiles
type UserDataProfilesReadonlyService struct {
	*userDataProfileCommon.UserDataProfilesService

	stateReadonlyService     statenode.IStateReadService
	nativeEnvironmentService environmentcommon.INativeEnvironmentService
}

// Constants for UserDataProfilesReadonlyService
const (
	PROFILES_KEY             = "userDataProfiles"
	PROFILE_ASSOCIATIONS_KEY = "profileAssociations"
)

// NewUserDataProfilesReadonlyService creates a new read-only user data profiles service
func NewUserDataProfilesReadonlyService(
	stateReadonlyService statenode.IStateReadService,
	uriIdentityService uriidentitycommon.IUriIdentityService,
	nativeEnvironmentService environmentcommon.INativeEnvironmentService,
	fileService filescommon.IFileService,
	logService logcommon.ILogService,
) *UserDataProfilesReadonlyService {
	service := &UserDataProfilesReadonlyService{
		stateReadonlyService:     stateReadonlyService,
		nativeEnvironmentService: nativeEnvironmentService,
	}

	// Initialize the base service
	service.UserDataProfilesService = userDataProfileCommon.NewUserDataProfilesService(
		nativeEnvironmentService,
		fileService,
		uriIdentityService,
		logService,
	)

	return service
}

// ServiceBrand implements the service brand
func (s *UserDataProfilesReadonlyService) ServiceBrand() interface{} {
	return "userDataProfilesReadonlyService"
}

// GetStoredProfiles retrieves stored profiles from state (override method)
func (s *UserDataProfilesReadonlyService) GetStoredProfiles() []*userDataProfileCommon.StoredUserDataProfile {
	storedProfilesState := s.stateReadonlyService.GetItem(PROFILES_KEY, []basecommon.UriDto[StoredUserDataProfileState]{})

	var storedProfiles []*userDataProfileCommon.StoredUserDataProfile

	// Handle the case where GetItem returns a slice of interfaces
	if stateArray, ok := storedProfilesState.([]interface{}); ok {
		for _, item := range stateArray {
			if stateMap, ok := item.(map[string]interface{}); ok {
				// Convert map to StoredUserDataProfileState
				jsonData, err := json.Marshal(stateMap)
				if err != nil {
					continue
				}

				var state StoredUserDataProfileState
				if err := json.Unmarshal(jsonData, &state); err != nil {
					continue
				}

				// Convert location: isString(p.location) ? this.uriIdentityService.extUri.joinPath(this.profilesHome, p.location) : URI.revive(p.location)
				var location *basecommon.URI
				if basecommon.IsString(state.Location) {
					locationStr := state.Location.(string)
					location = s.UriIdentityService().ExtUri().JoinPath(s.ProfilesHome(), locationStr)
				} else {
					// Revive URI from components
					if locationMap, ok := state.Location.(map[string]interface{}); ok {
						location = reviveURI(locationMap)
					}
				}

				if location != nil {
					storedProfile := &userDataProfileCommon.StoredUserDataProfile{
						Name:            state.Name,
						Location:        location,
						Icon:            state.Icon,
						UseDefaultFlags: state.UseDefaultFlags,
					}
					storedProfiles = append(storedProfiles, storedProfile)
				}
			}
		}
	}

	return storedProfiles
}

// GetStoredProfileAssociations retrieves stored profile associations from state (override method)
func (s *UserDataProfilesReadonlyService) GetStoredProfileAssociations() *userDataProfileCommon.StoredProfileAssociations {
	associations := s.stateReadonlyService.GetItem(PROFILE_ASSOCIATIONS_KEY, &userDataProfileCommon.StoredProfileAssociations{})

	if assoc, ok := associations.(*userDataProfileCommon.StoredProfileAssociations); ok {
		return assoc
	}

	// Try to convert from map
	if assocMap, ok := associations.(map[string]interface{}); ok {
		jsonData, err := json.Marshal(assocMap)
		if err != nil {
			return &userDataProfileCommon.StoredProfileAssociations{}
		}

		var result userDataProfileCommon.StoredProfileAssociations
		if err := json.Unmarshal(jsonData, &result); err != nil {
			return &userDataProfileCommon.StoredProfileAssociations{}
		}

		return &result
	}

	return &userDataProfileCommon.StoredProfileAssociations{}
}

// GetDefaultProfileExtensionsLocation returns the default profile extensions location (override method)
func (s *UserDataProfilesReadonlyService) GetDefaultProfileExtensionsLocation() *basecommon.URI {
	extensionsPath := s.nativeEnvironmentService.ExtensionsPath()
	if extensionsPath == "" {
		return nil
	}

	extensionsURI := basecommon.File(extensionsPath)
	// Change scheme to match profiles home: URI.file(this.nativeEnvironmentService.extensionsPath).with({ scheme: this.profilesHome.scheme })
	extensionsURI = extensionsURI.With(basecommon.UriComponents{
		Scheme: s.ProfilesHome().Scheme,
	})

	return s.UriIdentityService().ExtUri().JoinPath(extensionsURI, "extensions.json")
}

// UserDataProfilesService provides read-write access to user data profiles
type UserDataProfilesService struct {
	*UserDataProfilesReadonlyService

	stateService statenode.IStateService
}

// NewUserDataProfilesService creates a new user data profiles service
func NewUserDataProfilesService(
	stateService statenode.IStateService,
	uriIdentityService uriidentitycommon.IUriIdentityService,
	environmentService environmentcommon.INativeEnvironmentService,
	fileService filescommon.IFileService,
	logService logcommon.ILogService,
) *UserDataProfilesService {
	service := &UserDataProfilesService{
		stateService: stateService,
	}

	// Initialize the read-only service
	service.UserDataProfilesReadonlyService = NewUserDataProfilesReadonlyService(
		stateService,
		uriIdentityService,
		environmentService,
		fileService,
		logService,
	)

	return service
}

// ServiceBrand implements the service brand
func (s *UserDataProfilesService) ServiceBrand() interface{} {
	return "userDataProfilesService"
}

// SaveStoredProfiles saves stored profiles to state (override method)
func (s *UserDataProfilesService) SaveStoredProfiles(storedProfiles []*userDataProfileCommon.StoredUserDataProfile) {
	if len(storedProfiles) > 0 {
		// Convert profiles to state format: storedProfiles.map(profile => ({ ...profile, location: this.uriIdentityService.extUri.basename(profile.location) }))
		stateProfiles := make([]StoredUserDataProfileState, len(storedProfiles))
		for i, profile := range storedProfiles {
			stateProfiles[i] = StoredUserDataProfileState{
				StoredUserDataProfile: *profile,
				Location:              s.UriIdentityService().ExtUri().Basename(profile.Location),
			}
		}
		s.stateService.SetItem(PROFILES_KEY, stateProfiles)
	} else {
		s.stateService.RemoveItem(PROFILES_KEY)
	}
}

// SaveStoredProfileAssociations saves stored profile associations to state (override method)
func (s *UserDataProfilesService) SaveStoredProfileAssociations(storedProfileAssociations *userDataProfileCommon.StoredProfileAssociations) {
	if storedProfileAssociations.EmptyWindows != nil || storedProfileAssociations.Workspaces != nil {
		s.stateService.SetItem(PROFILE_ASSOCIATIONS_KEY, storedProfileAssociations)
	} else {
		s.stateService.RemoveItem(PROFILE_ASSOCIATIONS_KEY)
	}
}

// ServerUserDataProfilesService provides server-side user data profiles service
type ServerUserDataProfilesService struct {
	*UserDataProfilesService
}

// NewServerUserDataProfilesService creates a new server user data profiles service
func NewServerUserDataProfilesService(
	uriIdentityService uriidentitycommon.IUriIdentityService,
	environmentService environmentcommon.INativeEnvironmentService,
	fileService filescommon.IFileService,
	logService logcommon.ILogService,
) *ServerUserDataProfilesService {
	// Create a state service with immediate save strategy: new StateService(SaveStrategy.IMMEDIATE, environmentService, logService, fileService)
	stateService := statenode.NewStateService(
		statenode.SaveStrategyImmediate,
		environmentService,
		logService,
		fileService,
	)

	service := &ServerUserDataProfilesService{}
	service.UserDataProfilesService = NewUserDataProfilesService(
		stateService,
		uriIdentityService,
		environmentService,
		fileService,
		logService,
	)

	return service
}

// ServiceBrand implements the service brand
func (s *ServerUserDataProfilesService) ServiceBrand() interface{} {
	return "serverUserDataProfilesService"
}

// Init initializes the server user data profiles service
func (s *ServerUserDataProfilesService) Init() error {
	// Initialize the state service first: await (this.stateService as StateService).init()
	if stateService, ok := s.stateService.(*statenode.StateService); ok {
		if err := stateService.Init(); err != nil {
			return err
		}
	}

	// Then initialize the base service: return super.init()
	s.UserDataProfilesService.Init()
	return nil
}

// Helper function to revive URI from map
func reviveURI(uriMap map[string]interface{}) *basecommon.URI {
	scheme, _ := uriMap["scheme"].(string)
	authority, _ := uriMap["authority"].(string)
	path, _ := uriMap["path"].(string)
	query, _ := uriMap["query"].(string)
	fragment, _ := uriMap["fragment"].(string)

	return basecommon.From(basecommon.UriComponents{
		Scheme:    scheme,
		Authority: authority,
		Path:      path,
		Query:     query,
		Fragment:  fragment,
	})
}
