/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"sync"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	storagecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/parts/storage/common"
	ipccommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/ipc/common"
	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
)

// IProfileStorageValueChanges represents profile storage value changes
type IProfileStorageValueChanges struct {
	Profile *IUserDataProfile                         `json:"profile"`
	Changes []*storagecommon.IStorageValueChangeEvent `json:"changes"`
}

// IProfileStorageChanges represents profile storage changes
type IProfileStorageChanges struct {
	TargetChanges []*IUserDataProfile            `json:"targetChanges"`
	ValueChanges  []*IProfileStorageValueChanges `json:"valueChanges"`
}

// IStorageValue represents a storage value with its target
type IStorageValue struct {
	Value  *string                     `json:"value"`
	Target storagecommon.StorageTarget `json:"target"`
}

// IStorageService represents the storage service interface (simplified to avoid import cycle)
type IStorageService interface {
	// HasScope returns whether the service has the given scope for the profile
	HasScope(profile *IUserDataProfile) bool

	// Keys returns all keys for the given scope and target
	Keys(scope storagecommon.StorageScope, target storagecommon.StorageTarget) []string

	// Get retrieves a value for the given key and scope
	Get(key string, scope storagecommon.StorageScope) *string

	// StoreAll stores multiple items at once
	StoreAll(items []*IStorageEntry, flush bool)

	// Initialize initializes the storage service
	Initialize() error

	// Flush flushes pending changes
	Flush() error

	// Dispose disposes the storage service
	Dispose()
}

// IStorageEntry represents a storage entry
type IStorageEntry struct {
	Key    string                      `json:"key"`
	Value  *string                     `json:"value"`
	Scope  storagecommon.StorageScope  `json:"scope"`
	Target storagecommon.StorageTarget `json:"target"`
}

// IUserDataProfileStorageService represents the user data profile storage service interface
type IUserDataProfileStorageService interface {
	// Service brand for type safety
	ServiceBrand() interface{}

	// OnDidChange emitted whenever data is updated or deleted in a profile storage
	// or target of a profile storage entry changes
	OnDidChange() basecommon.Event[*IProfileStorageChanges]

	// ReadStorageData returns the requested profile storage data
	ReadStorageData(profile *IUserDataProfile) (map[string]*IStorageValue, error)

	// UpdateStorageData updates the given profile storage data in the profile storage
	UpdateStorageData(profile *IUserDataProfile, data map[string]*string, target storagecommon.StorageTarget) error

	// WithProfileScopedStorageService calls a function with a storage service scoped to given profile
	WithProfileScopedStorageService(profile *IUserDataProfile, fn func(storageService IStorageService) (interface{}, error)) (interface{}, error)
}

// AbstractUserDataProfileStorageService provides a base implementation of IUserDataProfileStorageService
type AbstractUserDataProfileStorageService struct {
	*basecommon.DisposableStore

	// Properties
	storageService     IStorageService
	storageServicesMap *basecommon.DisposableMap[string, *StorageService]
	mutex              sync.RWMutex
}

// NewAbstractUserDataProfileStorageService creates a new abstract user data profile storage service
func NewAbstractUserDataProfileStorageService(
	persistStorages bool,
	storageService IStorageService,
) *AbstractUserDataProfileStorageService {
	service := &AbstractUserDataProfileStorageService{
		DisposableStore: basecommon.NewDisposableStore(),
		storageService:  storageService,
	}

	if persistStorages {
		service.storageServicesMap = basecommon.NewDisposableMap[string, *StorageService]()
		service.Register(service.storageServicesMap)
	}

	return service
}

// ServiceBrand implements the service brand
func (s *AbstractUserDataProfileStorageService) ServiceBrand() interface{} {
	return "userDataProfileStorageService"
}

// ReadStorageData reads storage data for the given profile
func (s *AbstractUserDataProfileStorageService) ReadStorageData(profile *IUserDataProfile) (map[string]*IStorageValue, error) {
	result, err := s.WithProfileScopedStorageService(profile, func(storageService IStorageService) (interface{}, error) {
		return s.getItems(storageService), nil
	})
	if err != nil {
		return nil, err
	}
	return result.(map[string]*IStorageValue), nil
}

// UpdateStorageData updates storage data for the given profile
func (s *AbstractUserDataProfileStorageService) UpdateStorageData(profile *IUserDataProfile, data map[string]*string, target storagecommon.StorageTarget) error {
	_, err := s.WithProfileScopedStorageService(profile, func(storageService IStorageService) (interface{}, error) {
		s.writeItems(storageService, data, target)
		return nil, nil
	})
	return err
}

// WithProfileScopedStorageService calls a function with a storage service scoped to the given profile
func (s *AbstractUserDataProfileStorageService) WithProfileScopedStorageService(profile *IUserDataProfile, fn func(storageService IStorageService) (interface{}, error)) (interface{}, error) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// Check if the main storage service has scope for this profile
	if s.storageService.HasScope(profile) {
		return fn(s.storageService)
	}

	// Get or create a scoped storage service
	var storageService *StorageService
	var isNewService bool = true

	if s.storageServicesMap != nil {
		existingService := s.storageServicesMap.Get(profile.ID)
		if existingService != nil {
			storageService = existingService
			isNewService = false
		}
	}

	if storageService == nil {
		// Create storage database for this profile
		storageDatabase, err := s.createStorageDatabase(profile)
		if err != nil {
			return nil, err
		}

		storageService = NewStorageService(storageDatabase)
		if s.storageServicesMap != nil {
			s.storageServicesMap.Set(profile.ID, storageService)
		}

		// Initialize the storage service
		if err := storageService.Initialize(); err != nil {
			if s.storageServicesMap != nil {
				s.storageServicesMap.Delete(profile.ID)
			} else {
				storageService.Dispose()
			}
			return nil, err
		}
	}

	// Execute the function
	result, err := fn(storageService)
	if err != nil {
		if s.storageServicesMap == nil || isNewService {
			storageService.Dispose()
		}
		return nil, err
	}

	// Flush the storage service
	if err := storageService.Flush(); err != nil {
		if s.storageServicesMap == nil || isNewService {
			storageService.Dispose()
		}
		return nil, err
	}

	// Dispose if not persisted
	if s.storageServicesMap == nil || isNewService {
		storageService.Dispose()
	}

	return result, nil
}

// getItems retrieves all items from the storage service
func (s *AbstractUserDataProfileStorageService) getItems(storageService IStorageService) map[string]*IStorageValue {
	result := make(map[string]*IStorageValue)

	populate := func(target storagecommon.StorageTarget) {
		for _, key := range storageService.Keys(storagecommon.StorageScopeProfile, target) {
			value := storageService.Get(key, storagecommon.StorageScopeProfile)
			result[key] = &IStorageValue{
				Value:  value,
				Target: target,
			}
		}
	}

	populate(storagecommon.StorageTargetUser)
	populate(storagecommon.StorageTargetMachine)

	return result
}

// writeItems writes items to the storage service
func (s *AbstractUserDataProfileStorageService) writeItems(storageService IStorageService, items map[string]*string, target storagecommon.StorageTarget) {
	entries := make([]*IStorageEntry, 0, len(items))
	for key, value := range items {
		entries = append(entries, &IStorageEntry{
			Key:    key,
			Value:  value,
			Scope:  storagecommon.StorageScopeProfile,
			Target: target,
		})
	}
	storageService.StoreAll(entries, true)
}

// createStorageDatabase creates a storage database for the given profile (abstract method)
func (s *AbstractUserDataProfileStorageService) createStorageDatabase(profile *IUserDataProfile) (storagecommon.IStorageDatabase, error) {
	// This is an abstract method that should be implemented by concrete classes
	panic("createStorageDatabase must be implemented by concrete classes")
}

// RemoteUserDataProfileStorageService implements IUserDataProfileStorageService for remote scenarios
type RemoteUserDataProfileStorageService struct {
	*AbstractUserDataProfileStorageService

	// Events
	onDidChange *basecommon.Emitter[*IProfileStorageChanges]

	// Services
	remoteService           ipccommon.IRemoteService
	userDataProfilesService IUserDataProfilesService
	logService              logcommon.ILogService
	disposable              *basecommon.MutableDisposable
}

// NewRemoteUserDataProfileStorageService creates a new remote user data profile storage service
func NewRemoteUserDataProfileStorageService(
	persistStorages bool,
	remoteService ipccommon.IRemoteService,
	userDataProfilesService IUserDataProfilesService,
	storageService IStorageService,
	logService logcommon.ILogService,
) *RemoteUserDataProfileStorageService {
	base := NewAbstractUserDataProfileStorageService(persistStorages, storageService)

	service := &RemoteUserDataProfileStorageService{
		AbstractUserDataProfileStorageService: base,
		remoteService:                         remoteService,
		userDataProfilesService:               userDataProfilesService,
		logService:                            logService,
		disposable:                            basecommon.NewMutableDisposable(),
	}

	// Register the disposable
	service.Register(service.disposable)

	// Set up the onDidChange emitter with lazy initialization
	service.onDidChange = basecommon.NewEmitter[*IProfileStorageChanges]()

	return service
}

// OnDidChange returns the event for profile storage changes
func (s *RemoteUserDataProfileStorageService) OnDidChange() basecommon.Event[*IProfileStorageChanges] {
	return s.onDidChange.Event()
}

// createStorageDatabase creates a storage database for the given profile
func (s *RemoteUserDataProfileStorageService) createStorageDatabase(profile *IUserDataProfile) (storagecommon.IStorageDatabase, error) {
	storageChannel := s.remoteService.GetChannel("storage")

	// Check if profile uses default storage
	if s.isProfileUsingDefaultStorage(profile) {
		return s.newApplicationStorageDatabaseClient(storageChannel), nil
	} else {
		return s.newProfileStorageDatabaseClient(storageChannel, profile), nil
	}
}

// Helper methods for creating storage database clients
func (s *RemoteUserDataProfileStorageService) newApplicationStorageDatabaseClient(channel ipccommon.IChannel) storagecommon.IStorageDatabase {
	// This would normally import from storageIpc, but to avoid import cycles,
	// we'll create a simplified implementation
	return &ApplicationStorageDatabaseClient{channel: channel}
}

func (s *RemoteUserDataProfileStorageService) newProfileStorageDatabaseClient(channel ipccommon.IChannel, profile *IUserDataProfile) storagecommon.IStorageDatabase {
	// This would normally import from storageIpc, but to avoid import cycles,
	// we'll create a simplified implementation
	return &ProfileStorageDatabaseClient{channel: channel, profile: profile}
}

// Simplified storage database client implementations to avoid import cycles

// IChannelLike represents a simplified channel interface to avoid import cycles
type IChannelLike interface {
	Call(command string, arg interface{}) (interface{}, error)
}

// ApplicationStorageDatabaseClient implements storage database client for application scope
type ApplicationStorageDatabaseClient struct {
	channel IChannelLike
}

// GetItems retrieves all items from storage
func (c *ApplicationStorageDatabaseClient) GetItems() (map[string]interface{}, error) {
	result, err := c.channel.Call("getItems", nil)
	if err != nil {
		return nil, err
	}
	if items, ok := result.(map[string]interface{}); ok {
		return items, nil
	}
	return make(map[string]interface{}), nil
}

// UpdateItems updates items in storage
func (c *ApplicationStorageDatabaseClient) UpdateItems(request *storagecommon.IUpdateRequest) error {
	_, err := c.channel.Call("updateItems", request)
	return err
}

// Close closes the storage database client
func (c *ApplicationStorageDatabaseClient) Close() error {
	return nil
}

// CheckIntegrity checks the integrity of the storage
func (c *ApplicationStorageDatabaseClient) CheckIntegrity(full bool) (string, error) {
	result, err := c.channel.Call("checkIntegrity", full)
	if err != nil {
		return "", err
	}
	if str, ok := result.(string); ok {
		return str, nil
	}
	return "", nil
}

// ProfileStorageDatabaseClient implements storage database client for profile scope
type ProfileStorageDatabaseClient struct {
	channel ipccommon.IChannel
	profile *IUserDataProfile
}

// GetItems retrieves all items from storage
func (c *ProfileStorageDatabaseClient) GetItems() (map[string]interface{}, error) {
	request := map[string]interface{}{
		"profile": c.profile,
	}
	result, err := c.channel.Call("getItems", request)
	if err != nil {
		return nil, err
	}
	if items, ok := result.(map[string]interface{}); ok {
		return items, nil
	}
	return make(map[string]interface{}), nil
}

// UpdateItems updates items in storage
func (c *ProfileStorageDatabaseClient) UpdateItems(request *storagecommon.IUpdateRequest) error {
	requestWithProfile := map[string]interface{}{
		"profile": c.profile,
		"request": request,
	}
	_, err := c.channel.Call("updateItems", requestWithProfile)
	return err
}

// Close closes the storage database client
func (c *ProfileStorageDatabaseClient) Close() error {
	return nil
}

// CheckIntegrity checks the integrity of the storage
func (c *ProfileStorageDatabaseClient) CheckIntegrity(full bool) (string, error) {
	request := map[string]interface{}{
		"profile": c.profile,
		"full":    full,
	}
	result, err := c.channel.Call("checkIntegrity", request)
	if err != nil {
		return "", err
	}
	if str, ok := result.(string); ok {
		return str, nil
	}
	return "", nil
}

// Helper method to check if profile uses default storage
func (s *RemoteUserDataProfileStorageService) isProfileUsingDefaultStorage(profile *IUserDataProfile) bool {
	if profile == nil {
		return false
	}

	// Check if it's the default profile
	if profile.IsDefault {
		return true
	}

	// Check if globalState flag is set to use default
	if profile.UseDefaultFlags != nil {
		if globalStateFlag, exists := (*profile.UseDefaultFlags)[ProfileResourceTypeGlobalState]; exists && globalStateFlag {
			return true
		}
	}

	return false
}

// Helper method to revive profile (simplified implementation)
func (s *RemoteUserDataProfileStorageService) reviveProfile(profile *IUserDataProfile) *IUserDataProfile {
	// In a real implementation, this would use the userDataProfilesService to revive the profile
	// For now, just return the profile as-is
	return profile
}

// StorageService is a concrete implementation of storage service for profiles
type StorageService struct {
	profileStorage         storagecommon.IStorage
	profileStorageDatabase storagecommon.IStorageDatabase
	mutex                  sync.RWMutex
	disposed               bool
}

// NewStorageService creates a new storage service
func NewStorageService(profileStorageDatabase storagecommon.IStorageDatabase) *StorageService {
	return &StorageService{
		profileStorageDatabase: profileStorageDatabase,
	}
}

// HasScope returns whether the service has the given scope for the profile
func (s *StorageService) HasScope(profile *IUserDataProfile) bool {
	// For simplicity, always return false to force creation of scoped services
	return false
}

// Keys returns all keys for the given scope and target
func (s *StorageService) Keys(scope storagecommon.StorageScope, target storagecommon.StorageTarget) []string {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if scope == storagecommon.StorageScopeProfile && s.profileStorage != nil {
		return s.profileStorage.Keys()
	}
	return []string{}
}

// Get retrieves a value for the given key and scope
func (s *StorageService) Get(key string, scope storagecommon.StorageScope) *string {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if scope == storagecommon.StorageScopeProfile && s.profileStorage != nil {
		value := s.profileStorage.Get(key, "")
		if value != nil {
			if str, ok := value.(string); ok {
				return &str
			}
		}
	}
	return nil
}

// StoreAll stores multiple items at once
func (s *StorageService) StoreAll(items []*IStorageEntry, flush bool) {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.profileStorage != nil {
		for _, item := range items {
			if item.Scope == storagecommon.StorageScopeProfile {
				if item.Value != nil {
					s.profileStorage.Set(item.Key, *item.Value, item.Target)
				} else {
					s.profileStorage.Delete(item.Key)
				}
			}
		}

		if flush {
			s.profileStorage.Flush()
		}
	}
}

// Initialize initializes the storage service
func (s *StorageService) Initialize() error {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	// Create profile storage
	profileStorage, err := storagecommon.NewStorage(
		s.profileStorageDatabase,
		storagecommon.StorageScopeProfile,
		storagecommon.StorageTargetUser,
	)
	if err != nil {
		return err
	}

	s.profileStorage = profileStorage
	return nil
}

// Flush flushes pending changes
func (s *StorageService) Flush() error {
	s.mutex.RLock()
	defer s.mutex.RUnlock()

	if s.profileStorage != nil {
		return s.profileStorage.Flush()
	}
	return nil
}

// Dispose disposes the storage service
func (s *StorageService) Dispose() {
	s.mutex.Lock()
	defer s.mutex.Unlock()

	if s.disposed {
		return
	}

	s.disposed = true

	if s.profileStorage != nil {
		s.profileStorage.Close()
		s.profileStorage = nil
	}

	if s.profileStorageDatabase != nil {
		s.profileStorageDatabase.Close()
		s.profileStorageDatabase = nil
	}
}
