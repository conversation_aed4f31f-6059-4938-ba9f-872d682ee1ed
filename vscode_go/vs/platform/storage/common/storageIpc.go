/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"sync"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	ipccommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/parts/ipc/common"
	storagecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/parts/storage/common"
	userDataProfilecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/userDataProfile/common"
	workspacecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/workspace/common"
)

// IBaseSerializableStorageRequest represents the base serializable storage request
type IBaseSerializableStorageRequest struct {
	Profile   *userDataProfilecommon.IUserDataProfile `json:"profile,omitempty"`
	Workspace workspacecommon.IAnyWorkspaceIdentifier `json:"workspace,omitempty"`
}

// ISerializableUpdateRequest represents a serializable update request
type ISerializableUpdateRequest struct {
	IBaseSerializableStorageRequest
	Payload interface{} `json:"payload"`
}

// ISerializableItemsChangeEvent represents serializable items change event
type ISerializableItemsChangeEvent struct {
	Changed [][]string `json:"changed,omitempty"` // Array of [key, value] pairs
	Deleted []string   `json:"deleted,omitempty"`
}

// BaseStorageDatabaseClient provides base functionality for storage database clients
type BaseStorageDatabaseClient struct {
	*basecommon.DisposableStore

	channel   ipccommon.IChannel
	profile   *userDataProfilecommon.IUserDataProfile
	workspace workspacecommon.IAnyWorkspaceIdentifier

	onDidChangeItemsExternal *basecommon.Emitter[*storagecommon.IStorageItemsChangeEvent]
	mutex                    sync.RWMutex
}

// NewBaseStorageDatabaseClient creates a new base storage database client
func NewBaseStorageDatabaseClient(
	channel ipccommon.IChannel,
	profile *userDataProfilecommon.IUserDataProfile,
	workspace workspacecommon.IAnyWorkspaceIdentifier,
) *BaseStorageDatabaseClient {
	client := &BaseStorageDatabaseClient{
		DisposableStore:          basecommon.NewDisposableStore(),
		channel:                  channel,
		profile:                  profile,
		workspace:                workspace,
		onDidChangeItemsExternal: basecommon.NewEmitter[*storagecommon.IStorageItemsChangeEvent](),
	}

	return client
}

// OnDidChangeItemsExternal returns the event for external items changes
func (c *BaseStorageDatabaseClient) OnDidChangeItemsExternal() basecommon.Event[*storagecommon.IStorageItemsChangeEvent] {
	return c.onDidChangeItemsExternal.Event()
}

// GetItems retrieves all items from storage
func (c *BaseStorageDatabaseClient) GetItems() (map[string]interface{}, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	request := &IBaseSerializableStorageRequest{
		Profile:   c.profile,
		Workspace: c.workspace,
	}

	items, err := c.channel.Call("getItems", request)
	if err != nil {
		return nil, err
	}

	// Convert items array to map
	if itemsArray, ok := items.([][]string); ok {
		result := make(map[string]interface{})
		for _, item := range itemsArray {
			if len(item) >= 2 {
				result[item[0]] = item[1]
			}
		}
		return result, nil
	}

	return make(map[string]interface{}), nil
}

// UpdateItems updates items in storage
func (c *BaseStorageDatabaseClient) UpdateItems(request *storagecommon.IUpdateRequest) error {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	serializableRequest := &ISerializableUpdateRequest{
		IBaseSerializableStorageRequest: IBaseSerializableStorageRequest{
			Profile:   c.profile,
			Workspace: c.workspace,
		},
		Payload: request,
	}

	_, err := c.channel.Call("updateItems", serializableRequest)
	return err
}

// Close closes the storage database client
func (c *BaseStorageDatabaseClient) Close() error {
	c.Dispose()
	return nil
}

// CheckIntegrity checks the integrity of the storage
func (c *BaseStorageDatabaseClient) CheckIntegrity(full bool) (string, error) {
	c.mutex.RLock()
	defer c.mutex.RUnlock()

	request := &ISerializableUpdateRequest{
		IBaseSerializableStorageRequest: IBaseSerializableStorageRequest{
			Profile:   c.profile,
			Workspace: c.workspace,
		},
		Payload: full,
	}

	result, err := c.channel.Call("checkIntegrity", request)
	if err != nil {
		return "", err
	}

	if str, ok := result.(string); ok {
		return str, nil
	}

	return "", nil
}

// onDidChangeStorage handles storage change events
func (c *BaseStorageDatabaseClient) onDidChangeStorage(e *ISerializableItemsChangeEvent) {
	if len(e.Changed) > 0 || len(e.Deleted) > 0 {
		var changed map[string]interface{}
		var deleted map[string]bool

		if len(e.Changed) > 0 {
			changed = make(map[string]interface{})
			for _, item := range e.Changed {
				if len(item) >= 2 {
					changed[item[0]] = item[1]
				}
			}
		}

		if len(e.Deleted) > 0 {
			deleted = make(map[string]bool)
			for _, key := range e.Deleted {
				deleted[key] = true
			}
		}

		c.onDidChangeItemsExternal.Fire(&storagecommon.IStorageItemsChangeEvent{
			Changed: changed,
			Deleted: deleted,
		})
	}
}

// BaseProfileAwareStorageDatabaseClient extends BaseStorageDatabaseClient with profile awareness
type BaseProfileAwareStorageDatabaseClient struct {
	*BaseStorageDatabaseClient
}

// NewBaseProfileAwareStorageDatabaseClient creates a new profile-aware storage database client
func NewBaseProfileAwareStorageDatabaseClient(
	channel ipccommon.IChannel,
	profile *userDataProfilecommon.IUserDataProfile,
) *BaseProfileAwareStorageDatabaseClient {
	base := NewBaseStorageDatabaseClient(channel, profile, nil)
	return &BaseProfileAwareStorageDatabaseClient{
		BaseStorageDatabaseClient: base,
	}
}

// ApplicationStorageDatabaseClient implements storage database client for application scope
type ApplicationStorageDatabaseClient struct {
	*BaseProfileAwareStorageDatabaseClient
}

// NewApplicationStorageDatabaseClient creates a new application storage database client
func NewApplicationStorageDatabaseClient(channel ipccommon.IChannel) *ApplicationStorageDatabaseClient {
	base := NewBaseProfileAwareStorageDatabaseClient(channel, nil)
	return &ApplicationStorageDatabaseClient{
		BaseProfileAwareStorageDatabaseClient: base,
	}
}

// Close closes the application storage database client
func (c *ApplicationStorageDatabaseClient) Close() error {
	// The application storage database is shared across all instances so
	// we do not close it from the window. However we dispose the
	// listener for external changes because we no longer interested in it.
	c.Dispose()
	return nil
}

// ProfileStorageDatabaseClient implements storage database client for profile scope
type ProfileStorageDatabaseClient struct {
	*BaseProfileAwareStorageDatabaseClient
}

// NewProfileStorageDatabaseClient creates a new profile storage database client
func NewProfileStorageDatabaseClient(
	channel ipccommon.IChannel,
	profile *userDataProfilecommon.IUserDataProfile,
) *ProfileStorageDatabaseClient {
	base := NewBaseProfileAwareStorageDatabaseClient(channel, profile)
	return &ProfileStorageDatabaseClient{
		BaseProfileAwareStorageDatabaseClient: base,
	}
}

// Close closes the profile storage database client
func (c *ProfileStorageDatabaseClient) Close() error {
	// The profile storage database is shared across all instances of
	// the same profile so we do not close it from the window.
	// However we dispose the listener for external changes because
	// we no longer interested in it.
	c.Dispose()
	return nil
}

// WorkspaceStorageDatabaseClient implements storage database client for workspace scope
type WorkspaceStorageDatabaseClient struct {
	*BaseStorageDatabaseClient
}

// NewWorkspaceStorageDatabaseClient creates a new workspace storage database client
func NewWorkspaceStorageDatabaseClient(
	channel ipccommon.IChannel,
	workspace workspacecommon.IAnyWorkspaceIdentifier,
) *WorkspaceStorageDatabaseClient {
	base := NewBaseStorageDatabaseClient(channel, nil, workspace)
	client := &WorkspaceStorageDatabaseClient{
		BaseStorageDatabaseClient: base,
	}

	// Override onDidChangeItemsExternal to be Event.None for workspace storage
	client.onDidChangeItemsExternal = basecommon.NewEmitter[*storagecommon.IStorageItemsChangeEvent]()

	return client
}

// OnDidChangeItemsExternal returns Event.None for workspace storage
func (c *WorkspaceStorageDatabaseClient) OnDidChangeItemsExternal() basecommon.Event[*storagecommon.IStorageItemsChangeEvent] {
	// Unsupported for workspace storage because we only ever write from one window
	return basecommon.EventNone[*storagecommon.IStorageItemsChangeEvent]()
}

// Close closes the workspace storage database client
func (c *WorkspaceStorageDatabaseClient) Close() error {
	// The workspace storage database is only used in this instance
	// but we do not need to close it from here, the main process
	// can take care of that.
	c.Dispose()
	return nil
}

// StorageClient provides storage client functionality
type StorageClient struct {
	channel ipccommon.IChannel
}

// NewStorageClient creates a new storage client
func NewStorageClient(channel ipccommon.IChannel) *StorageClient {
	return &StorageClient{
		channel: channel,
	}
}

// IsUsed checks if a storage path is used
func (c *StorageClient) IsUsed(path string) (bool, error) {
	request := &ISerializableUpdateRequest{
		IBaseSerializableStorageRequest: IBaseSerializableStorageRequest{
			Profile:   nil,
			Workspace: nil,
		},
		Payload: path,
	}

	result, err := c.channel.Call("isUsed", request)
	if err != nil {
		return false, err
	}

	if used, ok := result.(bool); ok {
		return used, nil
	}

	return false, nil
}
