/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	storagecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/parts/storage/common"
)

// Constants
const (
	IsNewKey  = "__$__isNewStorageMarker"
	TargetKey = "__$__targetStorageMarker"
)

// WillSaveStateReason represents the reason for saving state
type WillSaveStateReason int

const (
	// WillSaveStateReasonNone - No specific reason to save state
	WillSaveStateReasonNone WillSaveStateReason = iota
	// WillSaveStateReasonShutdown - A hint that the workbench is about to shutdown
	WillSaveStateReasonShutdown
)

// IWillSaveStateEvent represents a will save state event
type IWillSaveStateEvent struct {
	Reason WillSaveStateReason `json:"reason"`
}

// IStorageEntry represents a storage entry
type IStorageEntry struct {
	Key    string                      `json:"key"`
	Value  interface{}                 `json:"value"`
	Scope  storagecommon.StorageScope  `json:"scope"`
	Target storagecommon.StorageTarget `json:"target"`
}

// IWorkspaceStorageValueChangeEvent represents a workspace storage value change event
type IWorkspaceStorageValueChangeEvent struct {
	*storagecommon.IStorageValueChangeEvent
	Scope storagecommon.StorageScope `json:"scope"` // Always StorageScopeWorkspace
}

// IProfileStorageValueChangeEvent represents a profile storage value change event
type IProfileStorageValueChangeEvent struct {
	*storagecommon.IStorageValueChangeEvent
	Scope storagecommon.StorageScope `json:"scope"` // Always StorageScopeProfile
}

// IApplicationStorageValueChangeEvent represents an application storage value change event
type IApplicationStorageValueChangeEvent struct {
	*storagecommon.IStorageValueChangeEvent
	Scope storagecommon.StorageScope `json:"scope"` // Always StorageScopeApplication
}

// IStorageService represents the storage service interface
type IStorageService interface {
	// OnDidChangeValue emitted whenever data is updated or deleted on the given scope and optional key
	OnDidChangeValue(scope storagecommon.StorageScope, key *string, disposable *basecommon.DisposableStore) basecommon.Event[*storagecommon.IStorageValueChangeEvent]

	// OnDidChangeTarget emitted whenever target of a storage entry changes
	OnDidChangeTarget() basecommon.Event[*storagecommon.IStorageTargetChangeEvent]

	// OnWillSaveState emitted when the storage is about to persist
	OnWillSaveState() basecommon.Event[*IWillSaveStateEvent]

	// Get retrieves an element stored with the given key from storage
	Get(key string, scope storagecommon.StorageScope, fallbackValue string) string
	GetOptional(key string, scope storagecommon.StorageScope) *string

	// GetBoolean retrieves a boolean element from storage
	GetBoolean(key string, scope storagecommon.StorageScope, fallbackValue bool) bool
	GetBooleanOptional(key string, scope storagecommon.StorageScope) *bool

	// GetNumber retrieves a numeric element from storage
	GetNumber(key string, scope storagecommon.StorageScope, fallbackValue float64) float64
	GetNumberOptional(key string, scope storagecommon.StorageScope) *float64

	// GetObject retrieves an object from storage
	GetObject(key string, scope storagecommon.StorageScope, fallbackValue interface{}) interface{}
	GetObjectOptional(key string, scope storagecommon.StorageScope) interface{}

	// Store stores a value under the given key to storage
	Store(key string, value interface{}, scope storagecommon.StorageScope, target storagecommon.StorageTarget)

	// Remove removes the given key from storage
	Remove(key string, scope storagecommon.StorageScope)

	// Keys returns all keys stored in the given scope
	Keys(scope storagecommon.StorageScope, target storagecommon.StorageTarget) []string

	// LogStorage logs the contents of storage
	LogStorage()

	// Migrate migrates storage from one scope to another
	Migrate(toWorkspace *IAnyWorkspaceIdentifier) error

	// IsNew returns whether the storage for the given scope is new
	IsNew(scope storagecommon.StorageScope) bool

	// Flush forces a flush of pending changes
	Flush(reason WillSaveStateReason) error

	// Switch switches the storage to a different workspace/profile
	Switch(toWorkspace *IAnyWorkspaceIdentifier, preserveData bool) error

	// SwitchToProfile switches to a different profile
	SwitchToProfile(toProfile *IUserDataProfile, preserveData bool) error

	// SwitchToWorkspace switches to a different workspace
	SwitchToWorkspace(toWorkspace *IAnyWorkspaceIdentifier, preserveData bool) error

	// HasScope returns whether the service has the given scope
	HasScope(scope storagecommon.StorageScope) bool
}

// IAnyWorkspaceIdentifier represents any workspace identifier
type IAnyWorkspaceIdentifier struct {
	ID string `json:"id"`
}

// IUserDataProfile represents a user data profile
type IUserDataProfile struct {
	ID                string          `json:"id"`
	Name              string          `json:"name"`
	GlobalStorageHome *basecommon.URI `json:"globalStorageHome"`
}

// AbstractStorageService provides a base implementation of IStorageService
type AbstractStorageService struct {
	*basecommon.DisposableStore

	// Events
	onDidChangeTarget *basecommon.Emitter[*storagecommon.IStorageTargetChangeEvent]
	onWillSaveState   *basecommon.Emitter[*IWillSaveStateEvent]

	// State
	isLogKeyInfoShown bool
}

// NewAbstractStorageService creates a new abstract storage service
func NewAbstractStorageService() *AbstractStorageService {
	return &AbstractStorageService{
		DisposableStore:   basecommon.NewDisposableStore(),
		onDidChangeTarget: basecommon.NewEmitter[*storagecommon.IStorageTargetChangeEvent](),
		onWillSaveState:   basecommon.NewEmitter[*IWillSaveStateEvent](),
	}
}

// OnDidChangeTarget returns the event for target changes
func (s *AbstractStorageService) OnDidChangeTarget() basecommon.Event[*storagecommon.IStorageTargetChangeEvent] {
	return s.onDidChangeTarget.Event()
}

// OnWillSaveState returns the event for will save state
func (s *AbstractStorageService) OnWillSaveState() basecommon.Event[*IWillSaveStateEvent] {
	return s.onWillSaveState.Event()
}

// DoInitialize should be implemented by subclasses
func (s *AbstractStorageService) DoInitialize() error {
	panic("DoInitialize must be implemented by subclass")
}

// GetStorage should be implemented by subclasses
func (s *AbstractStorageService) GetStorage(scope storagecommon.StorageScope) storagecommon.IStorage {
	panic("GetStorage must be implemented by subclass")
}

// GetLogDetails should be implemented by subclasses
func (s *AbstractStorageService) GetLogDetails(scope storagecommon.StorageScope) string {
	panic("GetLogDetails must be implemented by subclass")
}

// ShouldFlushWhenIdle should be implemented by subclasses
func (s *AbstractStorageService) ShouldFlushWhenIdle() bool {
	panic("ShouldFlushWhenIdle must be implemented by subclass")
}

// Get retrieves a value from storage
func (s *AbstractStorageService) Get(key string, scope storagecommon.StorageScope, fallbackValue string) string {
	storage := s.GetStorage(scope)
	if storage == nil {
		return fallbackValue
	}
	return storage.GetString(key, fallbackValue)
}

// GetOptional retrieves an optional value from storage
func (s *AbstractStorageService) GetOptional(key string, scope storagecommon.StorageScope) *string {
	storage := s.GetStorage(scope)
	if storage == nil || !storage.Has(key) {
		return nil
	}
	value := storage.GetString(key, "")
	return &value
}

// GetBoolean retrieves a boolean value from storage
func (s *AbstractStorageService) GetBoolean(key string, scope storagecommon.StorageScope, fallbackValue bool) bool {
	storage := s.GetStorage(scope)
	if storage == nil {
		return fallbackValue
	}
	return storage.GetBoolean(key, fallbackValue)
}

// GetBooleanOptional retrieves an optional boolean value from storage
func (s *AbstractStorageService) GetBooleanOptional(key string, scope storagecommon.StorageScope) *bool {
	storage := s.GetStorage(scope)
	if storage == nil || !storage.Has(key) {
		return nil
	}
	value := storage.GetBoolean(key, false)
	return &value
}

// GetNumber retrieves a numeric value from storage
func (s *AbstractStorageService) GetNumber(key string, scope storagecommon.StorageScope, fallbackValue float64) float64 {
	storage := s.GetStorage(scope)
	if storage == nil {
		return fallbackValue
	}
	return storage.GetNumber(key, fallbackValue)
}

// GetNumberOptional retrieves an optional numeric value from storage
func (s *AbstractStorageService) GetNumberOptional(key string, scope storagecommon.StorageScope) *float64 {
	storage := s.GetStorage(scope)
	if storage == nil || !storage.Has(key) {
		return nil
	}
	value := storage.GetNumber(key, 0)
	return &value
}

// GetObject retrieves an object from storage
func (s *AbstractStorageService) GetObject(key string, scope storagecommon.StorageScope, fallbackValue interface{}) interface{} {
	storage := s.GetStorage(scope)
	if storage == nil {
		return fallbackValue
	}
	return storage.GetObject(key, fallbackValue)
}

// GetObjectOptional retrieves an optional object from storage
func (s *AbstractStorageService) GetObjectOptional(key string, scope storagecommon.StorageScope) interface{} {
	storage := s.GetStorage(scope)
	if storage == nil || !storage.Has(key) {
		return nil
	}
	return storage.GetObject(key, nil)
}

// Store stores a value in storage
func (s *AbstractStorageService) Store(key string, value interface{}, scope storagecommon.StorageScope, target storagecommon.StorageTarget) {
	storage := s.GetStorage(scope)
	if storage == nil {
		return
	}
	storage.Set(key, value, target)
}

// Remove removes a key from storage
func (s *AbstractStorageService) Remove(key string, scope storagecommon.StorageScope) {
	storage := s.GetStorage(scope)
	if storage == nil {
		return
	}
	storage.Delete(key)
}

// Keys returns all keys in storage for the given scope
func (s *AbstractStorageService) Keys(scope storagecommon.StorageScope, target storagecommon.StorageTarget) []string {
	storage := s.GetStorage(scope)
	if storage == nil {
		return []string{}
	}
	return storage.Keys()
}

// LogStorage logs the contents of storage
func (s *AbstractStorageService) LogStorage() {
	// Implementation would log storage contents
}

// IsNew returns whether the storage for the given scope is new
func (s *AbstractStorageService) IsNew(scope storagecommon.StorageScope) bool {
	storage := s.GetStorage(scope)
	if storage == nil {
		return true
	}
	return storage.IsNew()
}

// Flush forces a flush of pending changes
func (s *AbstractStorageService) Flush(reason WillSaveStateReason) error {
	// Emit will save state event
	s.onWillSaveState.Fire(&IWillSaveStateEvent{Reason: reason})

	// Flush all storages
	for _, scope := range []storagecommon.StorageScope{
		storagecommon.StorageScopeApplication,
		storagecommon.StorageScopeProfile,
		storagecommon.StorageScopeWorkspace,
	} {
		storage := s.GetStorage(scope)
		if storage != nil {
			if err := storage.Flush(); err != nil {
				return err
			}
		}
	}

	return nil
}

// OnDidChangeValue returns the event for value changes
func (s *AbstractStorageService) OnDidChangeValue(scope storagecommon.StorageScope, key *string, disposable *basecommon.DisposableStore) basecommon.Event[*storagecommon.IStorageValueChangeEvent] {
	storage := s.GetStorage(scope)
	if storage == nil {
		return basecommon.EventNone[*storagecommon.IStorageValueChangeEvent]()
	}
	return storage.OnDidChangeValue()
}

// Migrate migrates storage from one scope to another
func (s *AbstractStorageService) Migrate(toWorkspace *IAnyWorkspaceIdentifier) error {
	// Default implementation - should be overridden by subclasses
	return nil
}

// Switch switches the storage to a different workspace/profile
func (s *AbstractStorageService) Switch(toWorkspace *IAnyWorkspaceIdentifier, preserveData bool) error {
	// Default implementation - should be overridden by subclasses
	return nil
}

// SwitchToProfile switches to a different profile
func (s *AbstractStorageService) SwitchToProfile(toProfile *IUserDataProfile, preserveData bool) error {
	// Default implementation - should be overridden by subclasses
	return nil
}

// SwitchToWorkspace switches to a different workspace
func (s *AbstractStorageService) SwitchToWorkspace(toWorkspace *IAnyWorkspaceIdentifier, preserveData bool) error {
	// Default implementation - should be overridden by subclasses
	return nil
}

// HasScope returns whether the service has the given scope
func (s *AbstractStorageService) HasScope(scope storagecommon.StorageScope) bool {
	return s.GetStorage(scope) != nil
}

// IsProfileUsingDefaultStorage checks if a profile is using default storage
func IsProfileUsingDefaultStorage(profile interface{}) bool {
	// This is a simplified implementation that works with any profile type
	// In a real implementation, this would check the profile's properties
	return false
}
