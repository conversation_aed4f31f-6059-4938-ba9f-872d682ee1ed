/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"os/exec"
	"regexp"
	"runtime"
	"strings"
	"sync"
	"time"

	"github.com/yudaprama/kawai-agent/vscode_go/vs"
	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
	basenode "github.com/yudaprama/kawai-agent/vscode_go/vs/base/node"
	configurationcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/configuration/common"
	logcommon "github.com/yudaprama/kawai-agent/vscode_go/vs/platform/log/common"
)

var (
	unixShellEnvPromise *shellEnvPromise
	shellEnvMutex       sync.Mutex
)

// shellEnvPromise represents a cached shell environment promise
type shellEnvPromise struct {
	result map[string]string
	err    error
	done   bool
	mu     sync.RWMutex
}

// newShellEnvPromise creates a new shell environment promise
func newShellEnvPromise() *shellEnvPromise {
	return &shellEnvPromise{
		result: make(map[string]string),
		done:   false,
	}
}

// wait waits for the promise to complete and returns the result
func (p *shellEnvPromise) wait() (map[string]string, error) {
	p.mu.RLock()
	defer p.mu.RUnlock()

	if p.done {
		return p.result, p.err
	}

	// In a real implementation, this would use a condition variable
	// For now, just return empty map
	return make(map[string]string), nil
}

// complete completes the promise with the given result
func (p *shellEnvPromise) complete(result map[string]string, err error) {
	p.mu.Lock()
	defer p.mu.Unlock()

	p.result = result
	p.err = err
	p.done = true
}

// GetResolvedShellEnv resolves the shell environment by spawning a shell. This call will cache
// the shell spawning so that subsequent invocations use that cached result.
//
// Will return an error if:
// - we hit a timeout of MAX_SHELL_RESOLVE_TIME
// - any other error from spawning a shell to figure out the environment
func GetResolvedShellEnv(configurationService configurationcommon.IConfigurationService, logService logcommon.ILogService, env map[string]string) (map[string]string, error) {
	if runtime.GOOS == "windows" {
		logService.Trace("resolveShellEnv(): skipped (Windows)")
		return make(map[string]string), nil
	}

	shellEnvMutex.Lock()
	defer shellEnvMutex.Unlock()

	// Call this only once and cache the promise for
	// subsequent calls since this operation can be
	// expensive (spawns a process).
	if unixShellEnvPromise == nil {
		unixShellEnvPromise = newShellEnvPromise()

		go func() {
			timeoutValue := 10000 // default to 10 seconds
			if configurationService != nil {
				if configuredTimeoutValue := configurationService.GetValue("application.shellEnvironmentResolutionTimeout"); configuredTimeoutValue != nil {
					if timeoutFloat, ok := configuredTimeoutValue.(float64); ok {
						timeoutValue = basecommon.Clamp(int(timeoutFloat), 1, 120) * 1000 // convert from seconds
					}
				}
			}

			// Create context with timeout
			ctx, cancel := context.WithTimeout(context.Background(), time.Duration(timeoutValue)*time.Millisecond)
			defer cancel()

			// Resolve shell env and handle errors
			result, err := doResolveUnixShellEnv(ctx, logService)
			if err != nil {
				if basecommon.IsCancellationError(err) || ctx.Err() != nil {
					unixShellEnvPromise.complete(make(map[string]string), nil)
				} else {
					errorMsg := vs.Localize("resolveShellEnvError", "Unable to resolve your shell environment: %s", basecommon.ToErrorMessage(err))
					unixShellEnvPromise.complete(make(map[string]string), fmt.Errorf(errorMsg))
				}
			} else {
				unixShellEnvPromise.complete(result, nil)
			}
		}()
	}

	return unixShellEnvPromise.wait()
}

// doResolveUnixShellEnv performs the actual shell environment resolution
func doResolveUnixShellEnv(ctx context.Context, logService logcommon.ILogService) (map[string]string, error) {
	env := make(map[string]string)

	// Copy current environment
	for _, envVar := range os.Environ() {
		parts := strings.SplitN(envVar, "=", 2)
		if len(parts) == 2 {
			env[parts[0]] = parts[1]
		}
	}

	runAsNode := env["ELECTRON_RUN_AS_NODE"]
	logService.Trace(fmt.Sprintf("getUnixShellEnvironment#runAsNode: %s", runAsNode))

	noAttach := env["ELECTRON_NO_ATTACH_CONSOLE"]
	logService.Trace(fmt.Sprintf("getUnixShellEnvironment#noAttach: %s", noAttach))

	mark := strings.ReplaceAll(basecommon.GenerateUuid(), "-", "")
	if len(mark) > 12 {
		mark = mark[:12]
	}
	regex := regexp.MustCompile(mark + `({.*})` + mark)

	// Set up environment for shell execution
	env["ELECTRON_RUN_AS_NODE"] = "1"
	env["ELECTRON_NO_ATTACH_CONSOLE"] = "1"
	env["VSCODE_RESOLVING_ENVIRONMENT"] = "1"

	logService.Trace(fmt.Sprintf("getUnixShellEnvironment#env: %v", env))

	// Get system shell
	var targetOS basenode.OS
	switch runtime.GOOS {
	case "windows":
		targetOS = basenode.OSWindows
	case "darwin":
		targetOS = basenode.OSMacintosh
	default:
		targetOS = basenode.OSLinux
	}

	systemShellUnix, err := basenode.GetSystemShell(targetOS, env)
	if err != nil {
		return nil, err
	}
	logService.Trace(fmt.Sprintf("getUnixShellEnvironment#shell: %s", systemShellUnix))

	// Check for cancellation
	select {
	case <-ctx.Done():
		return nil, basecommon.Canceled()
	default:
	}

	// Handle popular non-POSIX shells
	shellName := basecommon.Basename(systemShellUnix)
	var command string
	var shellArgs []string
	extraArgs := ""

	// Get the executable path for the current process
	execPath, err := os.Executable()
	if err != nil {
		execPath = "go" // fallback
	}

	switch {
	case regexp.MustCompile(`^(?:pwsh|powershell)(?:-preview)?$`).MatchString(shellName):
		// Older versions of PowerShell removes double quotes sometimes so we use "double single quotes" which is how
		// you escape single quotes inside of a single quoted string.
		command = fmt.Sprintf("& '%s' %s -p '''%s'' + JSON.stringify(process.env) + ''%s'''", execPath, extraArgs, mark, mark)
		shellArgs = []string{"-Login", "-Command"}
	case shellName == "nu": // nushell requires ^ before quoted path to treat it as a command
		command = fmt.Sprintf("^'%s' %s -p '\"%s\" + JSON.stringify(process.env) + \"%s\"'", execPath, extraArgs, mark, mark)
		shellArgs = []string{"-i", "-l", "-c"}
	case shellName == "xonsh": // #200374: native implementation is shorter
		command = fmt.Sprintf("import os, json; print(\"%s\", json.dumps(dict(os.environ)), \"%s\")", mark, mark)
		shellArgs = []string{"-i", "-l", "-c"}
	default:
		command = fmt.Sprintf("'%s' %s -p '\"%s\" + JSON.stringify(process.env) + \"%s\"'", execPath, extraArgs, mark, mark)

		if shellName == "tcsh" || shellName == "csh" {
			shellArgs = []string{"-ic"}
		} else {
			shellArgs = []string{"-i", "-l", "-c"}
		}
	}

	logService.Trace(fmt.Sprintf("getUnixShellEnvironment#spawn: %v %s", shellArgs, command))

	// Create the command
	cmd := exec.CommandContext(ctx, systemShellUnix, append(shellArgs, command)...)

	// Set up environment
	envSlice := make([]string, 0, len(env))
	for key, value := range env {
		envSlice = append(envSlice, fmt.Sprintf("%s=%s", key, value))
	}
	cmd.Env = envSlice

	// Execute the command
	output, err := cmd.CombinedOutput()
	if err != nil {
		logService.Error(fmt.Sprintf("getUnixShellEnvironment#errorChildProcess: %s", basecommon.ToErrorMessage(err)))
		return nil, err
	}

	raw := string(output)
	logService.Trace(fmt.Sprintf("getUnixShellEnvironment#raw: %s", raw))

	// Check exit code
	if cmd.ProcessState != nil && !cmd.ProcessState.Success() {
		exitCode := cmd.ProcessState.ExitCode()
		return nil, fmt.Errorf(vs.Localize("resolveShellEnvExitError", "Unexpected exit code from spawned shell (code %d)", exitCode))
	}

	// Parse the output
	match := regex.FindStringSubmatch(raw)
	var rawStripped string
	if len(match) > 1 {
		rawStripped = match[1]
	} else {
		rawStripped = "{}"
	}

	var shellEnv map[string]string
	if err := json.Unmarshal([]byte(rawStripped), &shellEnv); err != nil {
		logService.Error(fmt.Sprintf("getUnixShellEnvironment#errorCaught: %s", basecommon.ToErrorMessage(err)))
		return nil, err
	}

	// Restore or remove environment variables
	if runAsNode != "" {
		shellEnv["ELECTRON_RUN_AS_NODE"] = runAsNode
	} else {
		delete(shellEnv, "ELECTRON_RUN_AS_NODE")
	}

	if noAttach != "" {
		shellEnv["ELECTRON_NO_ATTACH_CONSOLE"] = noAttach
	} else {
		delete(shellEnv, "ELECTRON_NO_ATTACH_CONSOLE")
	}

	delete(shellEnv, "VSCODE_RESOLVING_ENVIRONMENT")

	// https://github.com/microsoft/vscode/issues/22593#issuecomment-336050758
	delete(shellEnv, "XDG_RUNTIME_DIR")

	logService.Trace(fmt.Sprintf("getUnixShellEnvironment#result: %v", shellEnv))
	return shellEnv, nil
}

// ResetShellEnvCache resets the cached shell environment, forcing a new resolution on next call
func ResetShellEnvCache() {
	shellEnvMutex.Lock()
	defer shellEnvMutex.Unlock()
	unixShellEnvPromise = nil
}

// GetShellEnvCacheStatus returns the status of the shell environment cache
func GetShellEnvCacheStatus() bool {
	shellEnvMutex.Lock()
	defer shellEnvMutex.Unlock()
	return unixShellEnvPromise != nil
}
