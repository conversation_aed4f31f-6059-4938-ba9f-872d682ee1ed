/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"fmt"
	"time"
)

// Stopwatch represents a stopwatch for timing operations
type Stopwatch struct {
	startTime time.Time
	elapsed   time.Duration
	running   bool
}

// NewStopwatch creates a new stopwatch
func NewStopwatch() *Stopwatch {
	return &Stopwatch{}
}

// Start starts the stopwatch
func (s *Stopwatch) Start() {
	s.startTime = time.Now()
	s.running = true
}

// Stop stops the stopwatch and returns the elapsed time
func (s *Stopwatch) Stop() time.Duration {
	if s.running {
		s.elapsed = time.Since(s.startTime)
		s.running = false
	}
	return s.elapsed
}

// Reset resets the stopwatch
func (s *Stopwatch) Reset() {
	s.startTime = time.Time{}
	s.elapsed = 0
	s.running = false
}

// Elapsed returns the elapsed time
func (s *Stopwatch) Elapsed() time.Duration {
	if s.running {
		return time.Since(s.startTime)
	}
	return s.elapsed
}

// IsRunning returns true if the stopwatch is running
func (s *Stopwatch) IsRunning() bool {
	return s.running
}

// String returns a string representation of the elapsed time
func (s *Stopwatch) String() string {
	elapsed := s.Elapsed()
	return fmt.Sprintf("%.2fms", float64(elapsed.Nanoseconds())/1000000.0)
}

// ElapsedMilliseconds returns the elapsed time in milliseconds
func (s *Stopwatch) ElapsedMilliseconds() int64 {
	return s.Elapsed().Nanoseconds() / 1000000
}

// ElapsedSeconds returns the elapsed time in seconds
func (s *Stopwatch) ElapsedSeconds() float64 {
	return s.Elapsed().Seconds()
}

// CreateAndStart creates a new stopwatch and starts it
func CreateAndStart() *Stopwatch {
	sw := NewStopwatch()
	sw.Start()
	return sw
}
