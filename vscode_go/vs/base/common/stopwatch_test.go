/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"testing"
	"time"
)

func TestStopwatch(t *testing.T) {
	t.Run("BasicTiming", func(t *testing.T) {
		stopwatch := NewStopwatch()

		// Initially should be 0
		if stopwatch.Elapsed() != 0 {
			t.Error("Initial elapsed time should be 0")
		}

		// Start timing
		stopwatch.Start()

		// Sleep for a small amount
		time.Sleep(10 * time.Millisecond)

		// Stop timing
		stopwatch.Stop()

		elapsed := stopwatch.Elapsed()
		if elapsed <= 0 {
			t.Error("Elapsed time should be positive after timing")
		}

		// Should be at least 10ms
		if elapsed < 10*time.Millisecond {
			t.Errorf("Elapsed time should be at least 10ms, got %v", elapsed)
		}
	})

	t.Run("Reset", func(t *testing.T) {
		stopwatch := NewStopwatch()

		stopwatch.Start()
		time.Sleep(5 * time.Millisecond)
		stopwatch.Stop()

		// Should have some elapsed time
		if stopwatch.Elapsed() == 0 {
			t.Error("Should have elapsed time before reset")
		}

		// Reset
		stopwatch.Reset()

		// Should be 0 after reset
		if stopwatch.Elapsed() != 0 {
			t.Error("Elapsed time should be 0 after reset")
		}
	})

	t.Run("MultipleStartStop", func(t *testing.T) {
		stopwatch := NewStopwatch()

		// First timing
		stopwatch.Start()
		time.Sleep(5 * time.Millisecond)
		stopwatch.Stop()

		elapsed1 := stopwatch.Elapsed()

		// Second timing (should accumulate)
		stopwatch.Start()
		time.Sleep(5 * time.Millisecond)
		stopwatch.Stop()

		elapsed2 := stopwatch.Elapsed()

		// Second elapsed should be greater than first
		if elapsed2 <= elapsed1 {
			t.Error("Second elapsed time should be greater than first")
		}
	})

	t.Run("StartWithoutStop", func(t *testing.T) {
		stopwatch := NewStopwatch()

		stopwatch.Start()
		time.Sleep(5 * time.Millisecond)

		// Should have elapsed time even without stopping
		elapsed := stopwatch.Elapsed()
		if elapsed <= 0 {
			t.Error("Should have elapsed time even without stopping")
		}
	})
}
