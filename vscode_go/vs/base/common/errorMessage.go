/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package common

import (
	"fmt"
	"runtime"
	"strings"
)

// ToErrorMessage converts various error types to a string message
func ToErrorMessage(err interface{}) string {
	if err == nil {
		return ""
	}

	switch e := err.(type) {
	case error:
		return e.Error()
	case string:
		return e
	case fmt.Stringer:
		return e.String()
	default:
		return fmt.Sprintf("%v", err)
	}
}

// GetErrorStack gets the stack trace from an error
func GetErrorStack(err interface{}) string {
	if err == nil {
		return ""
	}

	// Try to get stack trace if available
	type stackTracer interface {
		StackTrace() string
	}

	if st, ok := err.(stackTracer); ok {
		return st.StackTrace()
	}

	// Fall back to runtime stack if no stack is available
	return GetCurrentStack()
}

// GetCurrentStack gets the current stack trace
func GetCurrentStack() string {
	buf := make([]byte, 1024)
	for {
		n := runtime.Stack(buf, false)
		if n < len(buf) {
			return string(buf[:n])
		}
		buf = make([]byte, 2*len(buf))
	}
}

// NormalizeErrorMessage normalizes an error message for consistent formatting
func NormalizeErrorMessage(message string) string {
	if message == "" {
		return message
	}

	// Remove excessive whitespace
	message = strings.TrimSpace(message)

	// Normalize newlines
	message = strings.ReplaceAll(message, "\r\n", "\n")
	message = strings.ReplaceAll(message, "\r", "\n")

	return message
}

// ErrorToString converts an error to a string with optional stack trace
func ErrorToString(err interface{}, includeStack bool) string {
	if err == nil {
		return ""
	}

	message := ToErrorMessage(err)
	if includeStack {
		stack := GetErrorStack(err)
		if stack != "" {
			return message + "\n" + stack
		}
	}

	return message
}

// IsErrorMessage checks if a string looks like an error message
func IsErrorMessage(str string) bool {
	if str == "" {
		return false
	}

	// Check for common error patterns
	lowerStr := strings.ToLower(str)
	errorIndicators := []string{
		"error:",
		"exception:",
		"failed:",
		"panic:",
		"fatal:",
		"cannot",
		"unable to",
		"permission denied",
		"access denied",
		"not found",
		"timeout",
		"connection refused",
	}

	for _, indicator := range errorIndicators {
		if strings.Contains(lowerStr, indicator) {
			return true
		}
	}

	return false
}

// WrapError wraps an error with additional context
func WrapError(err interface{}, context string) error {
	if err == nil {
		return nil
	}

	errorMsg := ToErrorMessage(err)
	if context == "" {
		return fmt.Errorf("%s", errorMsg)
	}

	return fmt.Errorf("%s: %s", context, errorMsg)
}

// UnwrapError unwraps an error to get the underlying cause
func UnwrapError(err error) error {
	type causer interface {
		Cause() error
	}

	for err != nil {
		if cause, ok := err.(causer); ok {
			err = cause.Cause()
		} else {
			break
		}
	}

	return err
}
