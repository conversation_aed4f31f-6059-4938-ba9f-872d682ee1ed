/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

package node

import (
	"os"
	"path/filepath"
	"runtime"
	"strings"

	basecommon "github.com/yudaprama/kawai-agent/vscode_go/vs/base/common"
)

// OS represents the operating system type
type OS int

const (
	OSWindows OS = iota
	OSMacintosh
	OSLinux
)

// GetSystemShell returns the system shell executable path for the given OS and environment
func GetSystemShell(targetOS OS, env map[string]string) (string, error) {
	switch targetOS {
	case OSWindows:
		return getWindowsShell(env), nil
	case OSMacintosh:
		return getMacShell(env), nil
	case OSLinux:
		return getLinuxShell(env), nil
	default:
		return getDefaultShell(env), nil
	}
}

// getWindowsShell returns the Windows shell
func getWindowsShell(env map[string]string) string {
	// Check for PowerShell first
	if pwsh := findPowerShell(env); pwsh != "" {
		return pwsh
	}

	// Fall back to cmd.exe
	comspec := env["COMSPEC"]
	if comspec == "" {
		comspec = env["ComSpec"] // Case-insensitive variant
	}
	if comspec == "" {
		// Default location
		return filepath.Join(getSystemRoot(env), "System32", "cmd.exe")
	}
	return comspec
}

// getMacShell returns the macOS shell
func getMacShell(env map[string]string) string {
	shell := env["SHELL"]
	if shell == "" {
		// Default to bash on macOS
		return "/bin/bash"
	}
	return shell
}

// getLinuxShell returns the Linux shell
func getLinuxShell(env map[string]string) string {
	shell := env["SHELL"]
	if shell == "" {
		// Common default shells
		defaultShells := []string{
			"/bin/bash",
			"/bin/sh",
			"/usr/bin/bash",
			"/usr/bin/sh",
		}

		for _, defaultShell := range defaultShells {
			if fileExists(defaultShell) {
				return defaultShell
			}
		}

		// Ultimate fallback
		return "/bin/sh"
	}
	return shell
}

// getDefaultShell returns a shell based on the current runtime OS
func getDefaultShell(env map[string]string) string {
	switch runtime.GOOS {
	case "windows":
		return getWindowsShell(env)
	case "darwin":
		return getMacShell(env)
	default:
		return getLinuxShell(env)
	}
}

// findPowerShell tries to find PowerShell on Windows
func findPowerShell(env map[string]string) string {
	// Check for PowerShell Core (pwsh) first
	programFiles := env["ProgramFiles"]
	if programFiles == "" {
		programFiles = "C:\\Program Files"
	}

	// Try PowerShell Core
	pwshPaths := []string{
		filepath.Join(programFiles, "PowerShell", "7", "pwsh.exe"),
		filepath.Join(programFiles, "PowerShell", "6", "pwsh.exe"),
	}

	for _, path := range pwshPaths {
		if fileExists(path) {
			return path
		}
	}

	// Try Windows PowerShell
	systemRoot := getSystemRoot(env)
	windowsPowerShellPaths := []string{
		filepath.Join(systemRoot, "System32", "WindowsPowerShell", "v1.0", "powershell.exe"),
		filepath.Join(systemRoot, "SysWOW64", "WindowsPowerShell", "v1.0", "powershell.exe"),
	}

	for _, path := range windowsPowerShellPaths {
		if fileExists(path) {
			return path
		}
	}

	return ""
}

// getSystemRoot returns the Windows system root directory
func getSystemRoot(env map[string]string) string {
	systemRoot := env["SystemRoot"]
	if systemRoot == "" {
		systemRoot = env["SYSTEMROOT"] // Case-insensitive variant
	}
	if systemRoot == "" {
		systemRoot = "C:\\Windows"
	}
	return systemRoot
}

// fileExists checks if a file exists
func fileExists(path string) bool {
	if path == "" {
		return false
	}
	info, err := os.Stat(path)
	return err == nil && !info.IsDir()
}

// GetCurrentShell returns the current shell for the running process
func GetCurrentShell() (string, error) {
	env := os.Environ()
	envMap := make(map[string]string)
	for _, envVar := range env {
		parts := strings.SplitN(envVar, "=", 2)
		if len(parts) == 2 {
			envMap[parts[0]] = parts[1]
		}
	}

	var targetOS OS
	switch runtime.GOOS {
	case "windows":
		targetOS = OSWindows
	case "darwin":
		targetOS = OSMacintosh
	default:
		targetOS = OSLinux
	}

	return GetSystemShell(targetOS, envMap)
}

// IsValidShell checks if the given path is a valid shell executable
func IsValidShell(shellPath string) bool {
	if shellPath == "" {
		return false
	}

	// Check if file exists and is executable
	info, err := os.Stat(shellPath)
	if err != nil {
		return false
	}

	if info.IsDir() {
		return false
	}

	// On Unix-like systems, check if the file is executable
	if runtime.GOOS != "windows" {
		mode := info.Mode()
		if mode&0111 == 0 { // No execute permission
			return false
		}
	}

	return true
}

// GetShellName extracts the shell name from a shell path
func GetShellName(shellPath string) string {
	if shellPath == "" {
		return ""
	}

	// Use the basename function from the path utilities
	return basecommon.Basename(shellPath)
}

// GetShellType determines the shell type from the shell path
func GetShellType(shellPath string) string {
	if shellPath == "" {
		return "unknown"
	}

	shellName := strings.ToLower(GetShellName(shellPath))

	// Remove common extensions
	shellName = strings.TrimSuffix(shellName, ".exe")

	switch {
	case strings.Contains(shellName, "pwsh"):
		return "pwsh"
	case strings.Contains(shellName, "powershell"):
		return "powershell"
	case strings.Contains(shellName, "bash"):
		return "bash"
	case strings.Contains(shellName, "zsh"):
		return "zsh"
	case strings.Contains(shellName, "fish"):
		return "fish"
	case strings.Contains(shellName, "cmd"):
		return "cmd"
	case strings.Contains(shellName, "sh"):
		return "sh"
	case strings.Contains(shellName, "tcsh"):
		return "tcsh"
	case strings.Contains(shellName, "csh"):
		return "csh"
	case strings.Contains(shellName, "nu"):
		return "nu"
	case strings.Contains(shellName, "xonsh"):
		return "xonsh"
	default:
		return "unknown"
	}
}

// ShellOptions represents options for shell execution
type ShellOptions struct {
	Cwd         string            `json:"cwd,omitempty"`
	Env         map[string]string `json:"env,omitempty"`
	Shell       string            `json:"shell,omitempty"`
	WindowsHide bool              `json:"windowsHide,omitempty"`
}

// GetShellOptions returns default shell options for the current system
func GetShellOptions() (*ShellOptions, error) {
	shell, err := GetCurrentShell()
	if err != nil {
		return nil, err
	}

	cwd, err := os.Getwd()
	if err != nil {
		cwd = ""
	}

	env := os.Environ()
	envMap := make(map[string]string)
	for _, envVar := range env {
		parts := strings.SplitN(envVar, "=", 2)
		if len(parts) == 2 {
			envMap[parts[0]] = parts[1]
		}
	}

	return &ShellOptions{
		Cwd:         cwd,
		Env:         envMap,
		Shell:       shell,
		WindowsHide: runtime.GOOS == "windows",
	}, nil
}
